package com.quantum.iceac.detection.checks.packet;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for BadPackets (invalid or malicious packets)
 */
public class BadPacketsCheck extends Check {

    private static final double MAX_YAW_CHANGE = 360.0;
    private static final double MAX_PITCH = 90.0;
    private static final double MIN_PITCH = -90.0;
    private static final int MAX_POSITION_CHANGES_PER_TICK = 5;
    
    private final Map<UUID, Float> lastYaw = new HashMap<>();
    private final Map<UUID, Float> lastPitch = new HashMap<>();
    private final Map<UUID, Location> lastLocation = new HashMap<>();
    private final Map<UUID, Integer> positionChangesThisTick = new HashMap<>();
    private final Map<UUID, Long> lastTickTime = new HashMap<>();
    
    public BadPacketsCheck(IceAC plugin) {
        super(plugin, "BadPackets", CheckType.PACKET);
        setDescription("Detects invalid or malicious packets");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        // Check for position changes per tick
        if (!lastTickTime.containsKey(uuid)) {
            lastTickTime.put(uuid, currentTime);
            positionChangesThisTick.put(uuid, 1);
        } else {
            long timeDiff = currentTime - lastTickTime.get(uuid);
            if (timeDiff < 50) { // Less than one tick (50ms)
                positionChangesThisTick.put(uuid, positionChangesThisTick.get(uuid) + 1);
                
                // Check if too many position changes in one tick
                if (positionChangesThisTick.get(uuid) > MAX_POSITION_CHANGES_PER_TICK) {
                    // Flag player for violation
                    flag(player, "Too many position changes in one tick (" + 
                            positionChangesThisTick.get(uuid) + " > " + MAX_POSITION_CHANGES_PER_TICK + ")");
                    LoggerUtil.debug(player.getName() + " failed BadPackets (Position spam: " + 
                            positionChangesThisTick.get(uuid) + " > " + MAX_POSITION_CHANGES_PER_TICK + ")");
                }
            } else {
                // Reset counter for new tick
                lastTickTime.put(uuid, currentTime);
                positionChangesThisTick.put(uuid, 1);
            }
        }
        
        // Check for invalid pitch
        float pitch = to.getPitch();
        if (pitch > MAX_PITCH || pitch < MIN_PITCH) {
            // Flag player for violation
            flag(player, "Invalid pitch value: " + pitch);
            LoggerUtil.debug(player.getName() + " failed BadPackets (Invalid pitch: " + pitch + ")");
        }
        
        // Check for impossible yaw change
        if (lastYaw.containsKey(uuid)) {
            float yaw = to.getYaw();
            float yawDiff = Math.abs(yaw - lastYaw.get(uuid));
            
            // Normalize yaw difference
            if (yawDiff > 180) {
                yawDiff = 360 - yawDiff;
            }
            
            // Check if yaw change is too large
            if (yawDiff > MAX_YAW_CHANGE) {
                // Flag player for violation
                flag(player, "Impossible yaw change: " + String.format("%.2f", yawDiff) + 
                        " > " + MAX_YAW_CHANGE);
                LoggerUtil.debug(player.getName() + " failed BadPackets (Yaw change: " + 
                        String.format("%.2f", yawDiff) + " > " + MAX_YAW_CHANGE + ")");
            }
        }
        
        // Check for teleport spoofing
        if (lastLocation.containsKey(uuid)) {
            Location lastLoc = lastLocation.get(uuid);
            double distance = from.distance(lastLoc);
            
            // Check if distance is too large without teleport
            if (distance > 10.0 && !playerData.isTeleported()) {
                // Flag player for violation
                flag(player, "Teleport spoofing (Distance: " + String.format("%.2f", distance) + ")");
                LoggerUtil.debug(player.getName() + " failed BadPackets (Teleport spoof: " + 
                        String.format("%.2f", distance) + ")");
            }
        }
        
        // Update last values
        lastYaw.put(uuid, to.getYaw());
        lastPitch.put(uuid, to.getPitch());
        lastLocation.put(uuid, to.clone());
        
        // Reset teleport flag
        playerData.setTeleported(false);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();
        
        // Clean up maps
        lastYaw.remove(uuid);
        lastPitch.remove(uuid);
        lastLocation.remove(uuid);
        positionChangesThisTick.remove(uuid);
        lastTickTime.remove(uuid);
    }
    
    /**
     * Called when a player is teleported
     * @param player The player
     */
    public void onPlayerTeleport(Player player) {
        PlayerData playerData = getPlayerData(player);
        if (playerData != null) {
            playerData.setTeleported(true);
        }
    }
    
    /**
     * Called when a player sends an invalid packet
     * @param player The player
     * @param packetName The packet name
     * @param reason The reason why the packet is invalid
     */
    public void onInvalidPacket(Player player, String packetName, String reason) {
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Flag player for violation
        flag(player, "Invalid packet: " + packetName + " (" + reason + ")");
        LoggerUtil.debug(player.getName() + " failed BadPackets (Invalid packet: " + 
                packetName + ", Reason: " + reason + ")");
    }
}