package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.utils.LoggerUtil;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.List;

/**
 * Command to toggle debug mode
 */
public class DebugCommand implements SubCommand {

    private final IceAC plugin;
    
    public DebugCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "debug";
    }
    
    @Override
    public String getDescription() {
        return "Toggles debug mode";
    }
    
    @Override
    public String getUsage() {
        return "/iceac debug [on|off]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        List<String> aliases = new ArrayList<>();
        aliases.add("verbose");
        return aliases;
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Toggle debug mode
        boolean enabled;
        
        if (args.length > 0) {
            if (args[0].equalsIgnoreCase("on")) {
                enabled = true;
            } else if (args[0].equalsIgnoreCase("off")) {
                enabled = false;
            } else {
                // Invalid argument, toggle current state
                enabled = !LoggerUtil.isDebugEnabled();
            }
        } else {
            // No argument, toggle current state
            enabled = !LoggerUtil.isDebugEnabled();
        }
        
        // Set debug state
        LoggerUtil.setDebugEnabled(enabled);
        
        // Update config
        plugin.getConfig().set("general.debug", enabled);
        plugin.saveConfig();
        
        // Send message
        if (enabled) {
            sender.sendMessage(MessageUtil.getMessage("general.debug-enabled"));
        } else {
            sender.sendMessage(MessageUtil.getMessage("general.debug-disabled"));
        }
        
        return true;
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (args.length == 1) {
            List<String> completions = new ArrayList<>();
            completions.add("on");
            completions.add("off");
            return completions;
        }
        
        return new ArrayList<>();
    }
}