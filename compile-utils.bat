@echo off
echo Compiling utils classes manually...

REM Create utils directory in build
if not exist "build\classes\java\main\com\quantum\iceac\utils" mkdir "build\classes\java\main\com\quantum\iceac\utils"

REM Compile LoggerUtil
echo Compiling LoggerUtil.java...
javac -cp "gradle\wrapper\*;build\classes\java\main" -d "build\classes\java\main" "src\main\java\com\quantum\iceac\utils\LoggerUtil.java"

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: LoggerUtil compiled!
) else (
    echo ERROR: LoggerUtil compilation failed!
)

REM Compile MessageUtil
echo Compiling MessageUtil.java...
javac -cp "gradle\wrapper\*;build\classes\java\main" -d "build\classes\java\main" "src\main\java\com\quantum\iceac\utils\MessageUtil.java"

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: MessageUtil compiled!
) else (
    echo ERROR: MessageUtil compilation failed!
)

echo.
echo Checking compiled utils:
dir "build\classes\java\main\com\quantum\iceac\utils"

pause
