package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Command to manage the whitelist
 */
public class WhitelistCommand implements SubCommand {

    private final IceAC plugin;
    
    public WhitelistCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "whitelist";
    }
    
    @Override
    public String getDescription() {
        return "Manages the whitelist";
    }
    
    @Override
    public String getUsage() {
        return "/iceac whitelist <add|remove|list> [player] [reason]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        List<String> aliases = new ArrayList<>();
        aliases.add("wl");
        aliases.add("exempt");
        return aliases;
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        if (args.length == 0) {
            sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", getUsage()));
            return true;
        }
        
        String action = args[0].toLowerCase();
        
        if (action.equals("list")) {
            // List whitelisted players
            listWhitelistedPlayers(sender);
            return true;
        } else if (action.equals("add")) {
            // Add player to whitelist
            if (args.length < 2) {
                sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", "/iceac whitelist add <player> [reason]"));
                return true;
            }
            
            String playerName = args[1];
            String reason = "Added by " + sender.getName();
            
            if (args.length > 2) {
                // Combine remaining args for reason
                StringBuilder reasonBuilder = new StringBuilder();
                for (int i = 2; i < args.length; i++) {
                    reasonBuilder.append(args[i]).append(" ");
                }
                reason = reasonBuilder.toString().trim();
            }
            
            addPlayerToWhitelist(sender, playerName, reason);
            return true;
        } else if (action.equals("remove")) {
            // Remove player from whitelist
            if (args.length < 2) {
                sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", "/iceac whitelist remove <player>"));
                return true;
            }
            
            String playerName = args[1];
            removePlayerFromWhitelist(sender, playerName);
            return true;
        } else {
            sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", getUsage()));
            return true;
        }
    }
    
    /**
     * Lists all whitelisted players
     * @param sender The command sender
     */
    private void listWhitelistedPlayers(CommandSender sender) {
        // Load whitelist file
        File whitelistFile = new File(plugin.getDataFolder(), "whitelist.yml");
        FileConfiguration whitelistConfig = YamlConfiguration.loadConfiguration(whitelistFile);
        
        // Get players section
        if (!whitelistConfig.contains("players") || whitelistConfig.getConfigurationSection("players") == null) {
            sender.sendMessage(MessageUtil.getMessage("commands.whitelist-list-header"));
            sender.sendMessage(MessageUtil.getMessage("commands.whitelist-list-empty"));
            sender.sendMessage(MessageUtil.getMessage("commands.whitelist-list-footer"));
            return;
        }
        
        // Get all whitelisted players
        List<String> whitelistedPlayers = new ArrayList<>();
        
        for (String uuidStr : whitelistConfig.getConfigurationSection("players").getKeys(false)) {
            String name = whitelistConfig.getString("players." + uuidStr + ".name", "Unknown");
            String reason = whitelistConfig.getString("players." + uuidStr + ".reason", "No reason");
            
            whitelistedPlayers.add(MessageUtil.getMessage("commands.whitelist-list-format", 
                    "%player%", name, 
                    "%reason%", reason));
        }
        
        // Send list
        sender.sendMessage(MessageUtil.getMessage("commands.whitelist-list-header"));
        
        if (whitelistedPlayers.isEmpty()) {
            sender.sendMessage(MessageUtil.getMessage("commands.whitelist-list-empty"));
        } else {
            for (String entry : whitelistedPlayers) {
                sender.sendMessage(entry);
            }
        }
        
        sender.sendMessage(MessageUtil.getMessage("commands.whitelist-list-footer"));
    }
    
    /**
     * Adds a player to the whitelist
     * @param sender The command sender
     * @param playerName The player name
     * @param reason The reason for whitelisting
     */
    private void addPlayerToWhitelist(CommandSender sender, String playerName, String reason) {
        // Get player UUID
        UUID uuid = null;
        String finalName = playerName;
        
        // Try to get online player first
        Player onlinePlayer = Bukkit.getPlayer(playerName);
        if (onlinePlayer != null) {
            uuid = onlinePlayer.getUniqueId();
            finalName = onlinePlayer.getName();
        } else {
            // Try to get offline player
            OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
            if (offlinePlayer.hasPlayedBefore()) {
                uuid = offlinePlayer.getUniqueId();
                if (offlinePlayer.getName() != null) {
                    finalName = offlinePlayer.getName();
                }
            }
        }
        
        if (uuid == null) {
            sender.sendMessage(MessageUtil.getMessage("errors.player-not-found", "%player%", playerName));
            return;
        }
        
        // Load whitelist file
        File whitelistFile = new File(plugin.getDataFolder(), "whitelist.yml");
        FileConfiguration whitelistConfig = YamlConfiguration.loadConfiguration(whitelistFile);
        
        // Add player to whitelist
        String uuidStr = uuid.toString();
        String path = "players." + uuidStr;
        
        whitelistConfig.set(path + ".name", finalName);
        whitelistConfig.set(path + ".reason", reason);
        whitelistConfig.set(path + ".date", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        whitelistConfig.set(path + ".checks", new ArrayList<String>());
        
        // Save whitelist file
        try {
            whitelistConfig.save(whitelistFile);
            
            // Send message
            sender.sendMessage(MessageUtil.getMessage("commands.whitelist-add", "%player%", finalName));
            
            // Update player data if online
            if (onlinePlayer != null && plugin.getDetectionEngine() != null && 
                plugin.getDetectionEngine().getPlayerDataManager() != null) {
                plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(onlinePlayer).setExempt(true);
            }
        } catch (IOException e) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cFailed to save whitelist file: " + e.getMessage());
        }
    }
    
    /**
     * Removes a player from the whitelist
     * @param sender The command sender
     * @param playerName The player name
     */
    private void removePlayerFromWhitelist(CommandSender sender, String playerName) {
        // Load whitelist file
        File whitelistFile = new File(plugin.getDataFolder(), "whitelist.yml");
        FileConfiguration whitelistConfig = YamlConfiguration.loadConfiguration(whitelistFile);
        
        // Find player in whitelist
        UUID uuid = null;
        String uuidStr = null;
        
        // Check if player is online
        Player onlinePlayer = Bukkit.getPlayer(playerName);
        if (onlinePlayer != null) {
            uuid = onlinePlayer.getUniqueId();
            uuidStr = uuid.toString();
        } else {
            // Check if player is in whitelist by name
            if (whitelistConfig.contains("players")) {
                for (String id : whitelistConfig.getConfigurationSection("players").getKeys(false)) {
                    String name = whitelistConfig.getString("players." + id + ".name");
                    if (name != null && name.equalsIgnoreCase(playerName)) {
                        uuidStr = id;
                        try {
                            uuid = UUID.fromString(id);
                        } catch (IllegalArgumentException e) {
                            // Invalid UUID, ignore
                        }
                        break;
                    }
                }
            }
            
            // If not found by name, try to get offline player
            if (uuidStr == null) {
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
                if (offlinePlayer.hasPlayedBefore()) {
                    uuid = offlinePlayer.getUniqueId();
                    uuidStr = uuid.toString();
                }
            }
        }
        
        if (uuidStr == null || !whitelistConfig.contains("players." + uuidStr)) {
            sender.sendMessage(MessageUtil.getMessage("errors.player-not-found", "%player%", playerName));
            return;
        }
        
        // Get player name from whitelist
        String finalName = whitelistConfig.getString("players." + uuidStr + ".name", playerName);
        
        // Remove player from whitelist
        whitelistConfig.set("players." + uuidStr, null);
        
        // Save whitelist file
        try {
            whitelistConfig.save(whitelistFile);
            
            // Send message
            sender.sendMessage(MessageUtil.getMessage("commands.whitelist-remove", "%player%", finalName));
            
            // Update player data if online
            if (onlinePlayer != null && plugin.getDetectionEngine() != null && 
                plugin.getDetectionEngine().getPlayerDataManager() != null) {
                plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(onlinePlayer).setExempt(false);
            }
        } catch (IOException e) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cFailed to save whitelist file: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            completions.add("add");
            completions.add("remove");
            completions.add("list");
        } else if (args.length == 2) {
            String action = args[0].toLowerCase();
            
            if (action.equals("add")) {
                // Add online players
                for (Player player : Bukkit.getOnlinePlayers()) {
                    completions.add(player.getName());
                }
            } else if (action.equals("remove")) {
                // Add whitelisted players
                File whitelistFile = new File(plugin.getDataFolder(), "whitelist.yml");
                FileConfiguration whitelistConfig = YamlConfiguration.loadConfiguration(whitelistFile);
                
                if (whitelistConfig.contains("players")) {
                    for (String uuid : whitelistConfig.getConfigurationSection("players").getKeys(false)) {
                        String name = whitelistConfig.getString("players." + uuid + ".name");
                        if (name != null) {
                            completions.add(name);
                        }
                    }
                }
            }
        }
        
        return completions;
    }
}