package com.quantum.iceac.detection.checks.packet;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for PingSpoof (manipulating ping)
 */
public class PingSpoofCheck extends Check {

    private static final int DEFAULT_MAX_PING_DIFFERENCE = 1000; // ms
    private static final int DEFAULT_MAX_PING = 2000; // ms
    private static final int DEFAULT_MIN_PING = 0; // ms
    private static final int SAMPLE_SIZE = 10;
    
    private final Map<UUID, Integer> lastPing = new HashMap<>();
    private final Map<UUID, Integer> pingSum = new HashMap<>();
    private final Map<UUID, Integer> pingCount = new HashMap<>();
    private final Map<UUID, Long> keepAliveTime = new HashMap<>();
    private final Map<UUID, Long> keepAliveResponseTime = new HashMap<>();
    
    public PingSpoofCheck(IceAC plugin) {
        super(plugin, "PingSpoof", CheckType.PACKET);
        setDescription("Detects manipulating ping");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();
        
        // Initialize maps
        lastPing.put(uuid, 0);
        pingSum.put(uuid, 0);
        pingCount.put(uuid, 0);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();
        
        // Clean up maps
        lastPing.remove(uuid);
        pingSum.remove(uuid);
        pingCount.remove(uuid);
        keepAliveTime.remove(uuid);
        keepAliveResponseTime.remove(uuid);
    }
    
    /**
     * Called when a keep alive packet is sent to a player
     * @param player The player
     */
    public void onKeepAliveSent(Player player) {
        UUID uuid = player.getUniqueId();
        keepAliveTime.put(uuid, System.currentTimeMillis());
    }
    
    /**
     * Called when a keep alive response is received from a player
     * @param player The player
     */
    public void onKeepAliveResponse(Player player) {
        UUID uuid = player.getUniqueId();
        
        // Skip if no keep alive was sent
        if (!keepAliveTime.containsKey(uuid)) {
            return;
        }
        
        // Calculate response time
        long responseTime = System.currentTimeMillis() - keepAliveTime.get(uuid);
        keepAliveResponseTime.put(uuid, responseTime);
        
        // Check for ping spoofing
        checkPingSpoofing(player);
    }
    
    /**
     * Checks if a player is spoofing their ping
     * @param player The player
     */
    private void checkPingSpoofing(Player player) {
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        
        // Skip if no response time
        if (!keepAliveResponseTime.containsKey(uuid)) {
            return;
        }
        
        // Get current ping
        int currentPing = getPing(player);
        long responseTime = keepAliveResponseTime.get(uuid);
        
        // Get max ping difference from rules if available
        int maxPingDifference = DEFAULT_MAX_PING_DIFFERENCE;
        int maxPing = DEFAULT_MAX_PING;
        int minPing = DEFAULT_MIN_PING;
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule pingDiffRule = ruleManager.getRule("packet.ping.max_difference");
            if (pingDiffRule != null) {
                maxPingDifference = (int) pingDiffRule.getValue();
            }
            
            Rule maxPingRule = ruleManager.getRule("packet.ping.max_value");
            if (maxPingRule != null) {
                maxPing = (int) maxPingRule.getValue();
            }
            
            Rule minPingRule = ruleManager.getRule("packet.ping.min_value");
            if (minPingRule != null) {
                minPing = (int) minPingRule.getValue();
            }
        }
        
        // Check for ping difference between reported and measured
        long pingDifference = Math.abs(currentPing - responseTime);
        if (pingDifference > maxPingDifference) {
            // Flag player for violation
            flag(player, "Ping difference too large (" + pingDifference + " > " + 
                    maxPingDifference + ", Reported: " + currentPing + ", Measured: " + responseTime + ")");
            LoggerUtil.debug(player.getName() + " failed PingSpoof (Difference: " + 
                    pingDifference + " > " + maxPingDifference + ", Reported: " + 
                    currentPing + ", Measured: " + responseTime + ")");
        }
        
        // Check for ping fluctuation
        if (lastPing.containsKey(uuid)) {
            int pingDiff = Math.abs(currentPing - lastPing.get(uuid));
            
            // Update ping average
            pingSum.put(uuid, pingSum.get(uuid) + currentPing);
            pingCount.put(uuid, pingCount.get(uuid) + 1);
            
            // Check if we have enough samples
            if (pingCount.get(uuid) >= SAMPLE_SIZE) {
                int avgPing = pingSum.get(uuid) / pingCount.get(uuid);
                
                // Check for abnormal ping
                if (currentPing > maxPing) {
                    // Flag player for violation
                    flag(player, "Ping too high (" + currentPing + " > " + maxPing + ")");
                    LoggerUtil.debug(player.getName() + " failed PingSpoof (High ping: " + 
                            currentPing + " > " + maxPing + ")");
                } else if (currentPing < minPing) {
                    // Flag player for violation
                    flag(player, "Ping too low (" + currentPing + " < " + minPing + ")");
                    LoggerUtil.debug(player.getName() + " failed PingSpoof (Low ping: " + 
                            currentPing + " < " + minPing + ")");
                }
                
                // Reset counters
                pingSum.put(uuid, 0);
                pingCount.put(uuid, 0);
            }
        }
        
        // Update last ping
        lastPing.put(uuid, currentPing);
    }
    
    /**
     * Gets the player's ping
     * @param player The player
     * @return The ping in milliseconds
     */
    private int getPing(Player player) {
        try {
            Object entityPlayer = player.getClass().getMethod("getHandle").invoke(player);
            return (int) entityPlayer.getClass().getField("ping").get(entityPlayer);
        } catch (Exception e) {
            return 0;
        }
    }
}