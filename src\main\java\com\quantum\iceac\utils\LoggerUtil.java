package com.quantum.iceac.utils;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Utility class for logging messages to the console
 */
public class LoggerUtil {

    private static Logger logger = Logger.getLogger("IceAC");
    private static boolean debugMode = false;

    /**
     * Initializes the logger utility
     * @param pluginLogger The plugin logger instance
     */
    public static void init(Logger pluginLogger) {
        LoggerUtil.logger = pluginLogger;
    }

    /**
     * Sets debug mode
     * @param enabled True if debug mode should be enabled
     */
    public static void setDebugMode(boolean enabled) {
        debugMode = enabled;
    }
    
    /**
     * Logs an info message to the console
     * @param message The message to log
     */
    public static void info(String message) {
        if (logger != null) {
            logger.info(message);
        }
    }

    /**
     * Logs a warning message to the console
     * @param message The message to log
     */
    public static void warning(String message) {
        if (logger != null) {
            logger.warning(message);
        }
    }

    /**
     * Logs a severe message to the console
     * @param message The message to log
     */
    public static void severe(String message) {
        if (logger != null) {
            logger.severe(message);
        }
    }

    /**
     * Logs a severe message to the console with an exception
     * @param message The message to log
     * @param throwable The exception to log
     */
    public static void severe(String message, Throwable throwable) {
        if (logger != null) {
            logger.log(Level.SEVERE, message, throwable);
        }
    }

    /**
     * Logs a debug message to the console if debug mode is enabled
     * @param message The message to log
     */
    public static void debug(String message) {
        if (debugMode && logger != null) {
            logger.info("[DEBUG] " + message);
        }
    }
}