# IceAC Rules Configuration
# This file contains adaptive rules that the anti-cheat system uses to adjust its detection thresholds

rules:
  # Movement Rules
  movement.speed.horizontal:
    value: 0.3
    min: 0.1
    max: 0.5
    description: "Controls the maximum horizontal speed allowed"
  
  movement.speed.vertical:
    value: 0.42
    min: 0.1
    max: 0.8
    description: "Controls the maximum vertical jump height"
  
  movement.speed.sprint:
    value: 0.3
    min: 0.1
    max: 0.5
    description: "Controls the maximum sprint speed multiplier"
  
  movement.speed.sneak:
    value: 0.15
    min: 0.05
    max: 0.3
    description: "Controls the maximum sneak speed multiplier"
  
  movement.speed.swim:
    value: 0.2
    min: 0.1
    max: 0.4
    description: "Controls the maximum swim speed multiplier"
  
  movement.speed.elytra:
    value: 1.5
    min: 0.5
    max: 3.0
    description: "Controls the maximum elytra speed multiplier"
  
  movement.speed.boat:
    value: 0.4
    min: 0.2
    max: 0.8
    description: "Controls the maximum boat speed multiplier"
  
  movement.speed.horse:
    value: 0.5
    min: 0.3
    max: 1.0
    description: "Controls the maximum horse speed multiplier"
  
  # Combat Rules
  combat.reach.attack:
    value: 3.0
    min: 2.5
    max: 4.5
    description: "Controls the maximum attack reach distance"
  
  combat.reach.block:
    value: 4.5
    min: 3.0
    max: 6.0
    description: "Controls the maximum block reach distance"
  
  combat.speed.attack:
    value: 0.9
    min: 0.5
    max: 1.5
    description: "Controls the minimum time between attacks"
  
  combat.speed.click:
    value: 20.0
    min: 10.0
    max: 30.0
    description: "Controls the maximum clicks per second"
  
  combat.angle.attack:
    value: 120.0
    min: 90.0
    max: 180.0
    description: "Controls the maximum attack angle in degrees"
  
  # Packet Rules
  packet.limit.movement:
    value: 20.0
    min: 10.0
    max: 40.0
    description: "Controls the maximum movement packets per second"
  
  packet.limit.interaction:
    value: 15.0
    min: 5.0
    max: 30.0
    description: "Controls the maximum interaction packets per second"
  
  packet.limit.flying:
    value: 25.0
    min: 15.0
    max: 50.0
    description: "Controls the maximum flying packets per second"
  
  # World Rules
  world.reach.block:
    value: 5.0
    min: 3.0
    max: 7.0
    description: "Controls the maximum block placement reach distance"
  
  world.speed.block:
    value: 0.05
    min: 0.01
    max: 0.1
    description: "Controls the minimum time between block placements"
  
  # Adaptation Settings
  adaptation:
    # How often to adapt rules (in ticks)
    interval: 6000
    
    # How much to adapt rules by (0.0 - 1.0)
    factor: 0.1
    
    # Whether to save adapted rules on shutdown
    save-on-shutdown: true
    
    # Whether to adapt rules based on server TPS
    adapt-to-tps: true
    
    # Whether to adapt rules based on player ping
    adapt-to-ping: true
    
    # Whether to adapt rules based on server load
    adapt-to-load: true