package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckManager;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.List;

/**
 * Command to display plugin status
 */
public class StatusCommand implements SubCommand {

    private final IceAC plugin;
    
    public StatusCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "status";
    }
    
    @Override
    public String getDescription() {
        return "Displays plugin status information";
    }
    
    @Override
    public String getUsage() {
        return "/iceac status";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        List<String> aliases = new ArrayList<>();
        aliases.add("info");
        aliases.add("stats");
        return aliases;
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Get check statistics
        CheckManager checkManager = plugin.getDetectionEngine().getCheckManager();
        int enabledChecks = 0;
        int totalChecks = 0;
        
        if (checkManager != null) {
            for (Check check : checkManager.getAllChecks()) {
                totalChecks++;
                if (check.isEnabled()) {
                    enabledChecks++;
                }
            }
        }
        
        // Get metrics
        int totalViolations = 0;
        int totalPunishments = 0;
        
        if (plugin.getMetricsManager() != null && 
            plugin.getMetricsManager().getMetricsCollector() != null) {
            totalViolations = plugin.getMetricsManager().getMetricsCollector().getTotalViolations();
            totalPunishments = plugin.getMetricsManager().getMetricsCollector().getTotalPunishments();
        }
        
        // Calculate uptime
        long uptime = System.currentTimeMillis() - plugin.getStartTime();
        
        // Send header
        sender.sendMessage(MessageUtil.getMessage("commands.status-header"));
        
        // Send version
        sender.sendMessage(MessageUtil.getMessage("commands.status-version", 
                "%version%", plugin.getDescription().getVersion()));
        
        // Send uptime
        sender.sendMessage(MessageUtil.getMessage("commands.status-uptime", 
                "%uptime%", MessageUtil.formatTime(uptime)));
        
        // Send checks
        sender.sendMessage(MessageUtil.getMessage("commands.status-checks", 
                "%enabled%", String.valueOf(enabledChecks), 
                "%total%", String.valueOf(totalChecks)));
        
        // Send violations
        sender.sendMessage(MessageUtil.getMessage("commands.status-violations", 
                "%violations%", String.valueOf(totalViolations)));
        
        // Send punishments
        sender.sendMessage(MessageUtil.getMessage("commands.status-punishments", 
                "%punishments%", String.valueOf(totalPunishments)));
        
        // Send footer
        sender.sendMessage(MessageUtil.getMessage("commands.status-footer"));
        
        return true;
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }
}