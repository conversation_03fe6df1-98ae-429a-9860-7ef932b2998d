package com.quantum.iceac.detection.checks.combat;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerMoveEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for Aim assistance (Aimbot, Aim assist)
 */
public class Aim<PERSON>heck extends Check {

    private static final double DEFAULT_MAX_AIM_CONSISTENCY = 0.95;
    private static final double DEFAULT_MIN_AIM_VARIATION = 0.1;
    private static final int DEFAULT_SAMPLE_SIZE = 20;
    private static final double DEFAULT_SUSPICIOUS_SNAP_ANGLE = 45.0;
    
    // Track aim patterns
    private final Map<UUID, Double> aimVariations = new HashMap<>();
    private final Map<UUID, Integer> snapCount = new HashMap<>();
    private final Map<UUID, Long> lastSnapTime = new HashMap<>();
    
    public AimCheck(IceAC plugin) {
        super(plugin, "Aim", CheckType.COMBAT, "Detects aim assistance and aimbot");
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Check if player moved their head
        if (event.getFrom().getYaw() == event.getTo().getYaw() && 
            event.getFrom().getPitch() == event.getTo().getPitch()) {
            return;
        }
        
        // Update aim data
        float yaw = event.getTo().getYaw();
        float pitch = event.getTo().getPitch();
        playerData.updateAim(yaw, pitch);
        
        // Check aim consistency
        checkAimConsistency(player, playerData);
        
        // Check for suspicious snapping
        checkAimSnapping(player, playerData, yaw, pitch);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Check aim accuracy during combat
        checkCombatAim(player, playerData);
    }
    
    /**
     * Checks aim consistency patterns
     * @param player The player
     * @param playerData The player data
     */
    private void checkAimConsistency(Player player, PlayerData playerData) {
        // Need enough samples
        if (playerData.getAimSamples() < DEFAULT_SAMPLE_SIZE) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        
        // Calculate aim variation
        double variation = calculateAimVariation(player, playerData);
        aimVariations.put(uuid, variation);
        
        // Get thresholds from config
        double maxConsistency = getPlugin().getConfig().getDouble("checks.aim.max-consistency", DEFAULT_MAX_AIM_CONSISTENCY);
        double minVariation = getPlugin().getConfig().getDouble("checks.aim.min-variation", DEFAULT_MIN_AIM_VARIATION);
        
        // Check if aim is too consistent (possible aimbot)
        if (variation < minVariation) {
            double consistency = 1.0 - variation;
            playerData.setAimConsistency(consistency);
            
            if (consistency > maxConsistency) {
                // Flag player for violation
                flag(player, "Aim too consistent (Variation: " + String.format("%.3f", variation) + 
                        ", Consistency: " + String.format("%.3f", consistency) + ")");
                LoggerUtil.debug(player.getName() + " failed Aim (Too consistent: " + 
                        String.format("%.3f", consistency) + ")");
            }
        }
    }
    
    /**
     * Checks for suspicious aim snapping
     * @param player The player
     * @param playerData The player data
     * @param yaw The current yaw
     * @param pitch The current pitch
     */
    private void checkAimSnapping(Player player, PlayerData playerData, float yaw, float pitch) {
        UUID uuid = player.getUniqueId();
        
        // Calculate angle change
        float lastYaw = playerData.getLastAimYaw();
        float lastPitch = playerData.getLastAimPitch();
        
        if (lastYaw == 0 && lastPitch == 0) {
            return; // First sample
        }
        
        double yawDiff = Math.abs(yaw - lastYaw);
        double pitchDiff = Math.abs(pitch - lastPitch);
        
        // Normalize yaw difference
        if (yawDiff > 180) {
            yawDiff = 360 - yawDiff;
        }
        
        double totalAngleChange = Math.sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff);
        
        // Get threshold from config
        double suspiciousSnapAngle = getPlugin().getConfig().getDouble("checks.aim.suspicious-snap-angle", DEFAULT_SUSPICIOUS_SNAP_ANGLE);
        
        // Check for suspicious snapping
        if (totalAngleChange > suspiciousSnapAngle) {
            long now = System.currentTimeMillis();
            long lastSnap = lastSnapTime.getOrDefault(uuid, 0L);
            
            // Check if snaps are happening too frequently
            if (now - lastSnap < 500) { // Within 500ms
                int snaps = snapCount.getOrDefault(uuid, 0) + 1;
                snapCount.put(uuid, snaps);
                
                if (snaps > 3) { // More than 3 snaps in short time
                    // Flag player for violation
                    flag(player, "Suspicious aim snapping (Angle: " + String.format("%.1f", totalAngleChange) + 
                            "°, Snaps: " + snaps + ")");
                    LoggerUtil.debug(player.getName() + " failed Aim (Snapping: " + 
                            String.format("%.1f", totalAngleChange) + "°, Count: " + snaps + ")");
                    
                    // Reset counter
                    snapCount.put(uuid, 0);
                }
            } else {
                // Reset counter if too much time passed
                snapCount.put(uuid, 1);
            }
            
            lastSnapTime.put(uuid, now);
        }
    }
    
    /**
     * Checks aim accuracy during combat
     * @param player The player
     * @param playerData The player data
     */
    private void checkCombatAim(Player player, PlayerData playerData) {
        // This could be expanded to check for perfect aim during combat
        // For now, we'll just record the combat event
        long now = System.currentTimeMillis();
        long lastAim = playerData.getLastAimTime();
        
        // Check if aim was adjusted right before attack (possible aimbot)
        if (now - lastAim < 100) { // Within 100ms
            double consistency = playerData.getAimConsistency();
            if (consistency > 0.9) {
                // Flag player for violation
                flag(player, "Perfect aim during combat (Consistency: " + String.format("%.3f", consistency) + ")");
                LoggerUtil.debug(player.getName() + " failed Aim (Combat aim: " + 
                        String.format("%.3f", consistency) + ")");
            }
        }
    }
    
    /**
     * Calculates aim variation for a player
     * @param player The player
     * @param playerData The player data
     * @return The aim variation
     */
    private double calculateAimVariation(Player player, PlayerData playerData) {
        // This is a simplified calculation
        // In a real implementation, you would track multiple aim samples
        // and calculate the standard deviation of aim movements
        
        // For now, return a basic variation based on recent movements
        UUID uuid = player.getUniqueId();
        Double lastVariation = aimVariations.get(uuid);
        
        if (lastVariation == null) {
            return 0.5; // Default variation
        }
        
        // Simulate variation calculation (this should be improved)
        return Math.max(0.01, lastVariation * 0.9 + Math.random() * 0.2);
    }
    
    @Override
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        aimVariations.remove(uuid);
        snapCount.remove(uuid);
        lastSnapTime.remove(uuid);
    }
}
