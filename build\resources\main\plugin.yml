name: IceAC
version: ${version}
main: com.quantum.iceac.IceAC
api-version: 1.19
authors: [Quantum Development Team]
description: Revolutionary Anti-Cheat Protection for Minecraft servers
website: https://quantum.dev/iceac
depend: [ProtocolLib]

permissions:
  iceac.admin:
    description: Allows access to all IceAC administrative commands
    default: op
  iceac.alerts:
    description: Allows receiving cheat detection alerts
    default: op
  iceac.bypass:
    description: Exempts player from cheat detection
    default: false

commands:
  iceac:
    description: Main command for IceAC
    usage: /iceac <subcommand>
    permission: iceac.admin
    aliases: [iac]