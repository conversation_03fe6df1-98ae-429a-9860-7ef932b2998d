package com.quantum.iceac.prevention.rules;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages adaptive rules for the anti-cheat system
 */
public class RuleManager {

    private final IceAC plugin;
    private final Map<String, Rule> rules;
    private File rulesFile;
    private FileConfiguration rulesConfig;
    private boolean initialized = false;
    
    public RuleManager(IceAC plugin) {
        this.plugin = plugin;
        this.rules = new HashMap<>();
    }
    
    /**
     * Initializes the rule manager
     */
    public void initialize() {
        // Check if rule adaptation is enabled
        if (!plugin.getConfig().getBoolean("prevention.rule-adaptation.enabled", true)) {
            LoggerUtil.info("Rule adaptation is disabled in the configuration.");
            return;
        }
        
        // Load rules file
        loadRulesFile();
        
        // Register default rules
        registerDefaultRules();
        
        // Load rules from configuration
        loadRules();
        
        initialized = true;
        LoggerUtil.info("Initialized rule manager with " + rules.size() + " rules.");
    }
    
    /**
     * Loads the rules file
     */
    private void loadRulesFile() {
        rulesFile = new File(plugin.getDataFolder(), "rules.yml");
        
        if (!rulesFile.exists()) {
            plugin.saveResource("rules.yml", false);
        }
        
        rulesConfig = YamlConfiguration.loadConfiguration(rulesFile);
        LoggerUtil.debug("Loaded rules file.");
    }
    
    /**
     * Registers default rules
     */
    private void registerDefaultRules() {
        // Register movement rules
        registerRule(new Rule("movement.speed.horizontal", 0.3, 0.1, 0.5, 
                "Controls the maximum horizontal speed allowed"));
        registerRule(new Rule("movement.speed.vertical", 0.42, 0.1, 0.8, 
                "Controls the maximum vertical jump height"));
        registerRule(new Rule("movement.speed.sprint", 0.3, 0.1, 0.5, 
                "Controls the maximum sprint speed multiplier"));
        
        // Register combat rules
        registerRule(new Rule("combat.reach.attack", 3.0, 2.5, 4.5, 
                "Controls the maximum attack reach distance"));
        registerRule(new Rule("combat.reach.block", 4.5, 3.0, 6.0, 
                "Controls the maximum block reach distance"));
        registerRule(new Rule("combat.speed.attack", 0.9, 0.5, 1.5, 
                "Controls the minimum time between attacks"));
        
        // Register packet rules
        registerRule(new Rule("packet.limit.movement", 20.0, 10.0, 40.0, 
                "Controls the maximum movement packets per second"));
        registerRule(new Rule("packet.limit.interaction", 15.0, 5.0, 30.0, 
                "Controls the maximum interaction packets per second"));
        
        LoggerUtil.debug("Registered default rules.");
    }
    
    /**
     * Loads rules from configuration
     */
    private void loadRules() {
        ConfigurationSection rulesSection = rulesConfig.getConfigurationSection("rules");
        
        if (rulesSection == null) {
            LoggerUtil.warning("No rules section found in rules.yml. Using default values.");
            return;
        }
        
        for (String key : rulesSection.getKeys(false)) {
            ConfigurationSection ruleSection = rulesSection.getConfigurationSection(key);
            
            if (ruleSection == null) {
                continue;
            }
            
            double value = ruleSection.getDouble("value", 0.0);
            double min = ruleSection.getDouble("min", 0.0);
            double max = ruleSection.getDouble("max", 1.0);
            String description = ruleSection.getString("description", "");
            
            Rule rule = new Rule(key, value, min, max, description);
            registerRule(rule);
            
            LoggerUtil.debug("Loaded rule: " + key + " = " + value);
        }
    }
    
    /**
     * Registers a rule
     * @param rule The rule to register
     */
    public void registerRule(Rule rule) {
        rules.put(rule.getName(), rule);
    }
    
    /**
     * Gets a rule by name
     * @param name The rule name
     * @return The rule, or null if not found
     */
    public Rule getRule(String name) {
        return rules.get(name);
    }
    
    /**
     * Gets the value of a rule
     * @param name The rule name
     * @return The rule value, or 0 if not found
     */
    public double getRuleValue(String name) {
        Rule rule = getRule(name);
        return rule != null ? rule.getValue() : 0.0;
    }
    
    /**
     * Sets the value of a rule
     * @param name The rule name
     * @param value The new value
     * @return True if successful, false otherwise
     */
    public boolean setRuleValue(String name, double value) {
        Rule rule = getRule(name);
        
        if (rule == null) {
            return false;
        }
        
        rule.setValue(value);
        return true;
    }
    
    /**
     * Adapts a rule based on server conditions
     * @param name The rule name
     * @param factor The adaptation factor (-1.0 to 1.0)
     * @return True if successful, false otherwise
     */
    public boolean adaptRule(String name, double factor) {
        Rule rule = getRule(name);
        
        if (rule == null) {
            return false;
        }
        
        // Clamp factor between -1.0 and 1.0
        factor = Math.max(-1.0, Math.min(1.0, factor));
        
        // Calculate new value
        double range = rule.getMax() - rule.getMin();
        double step = range * 0.1 * factor; // 10% of range * factor
        double newValue = rule.getValue() + step;
        
        // Clamp new value between min and max
        newValue = Math.max(rule.getMin(), Math.min(rule.getMax(), newValue));
        
        // Set new value
        rule.setValue(newValue);
        
        LoggerUtil.debug("Adapted rule " + name + " by factor " + factor + 
                " to new value " + newValue);
        
        return true;
    }
    
    /**
     * Saves rules to configuration
     */
    public void saveRules() {
        if (!initialized) {
            return;
        }
        
        // Clear rules section
        rulesConfig.set("rules", null);
        
        // Save rules
        for (Rule rule : rules.values()) {
            String path = "rules." + rule.getName();
            rulesConfig.set(path + ".value", rule.getValue());
            rulesConfig.set(path + ".min", rule.getMin());
            rulesConfig.set(path + ".max", rule.getMax());
            rulesConfig.set(path + ".description", rule.getDescription());
        }
        
        // Save file
        try {
            rulesConfig.save(rulesFile);
            LoggerUtil.debug("Saved rules to file.");
        } catch (IOException e) {
            LoggerUtil.severe("Failed to save rules file: " + e.getMessage());
        }
    }
    
    /**
     * Reloads rules from configuration
     */
    public void reloadRules() {
        if (!initialized) {
            return;
        }
        
        // Clear rules
        rules.clear();
        
        // Reload file
        rulesConfig = YamlConfiguration.loadConfiguration(rulesFile);
        
        // Register default rules
        registerDefaultRules();
        
        // Load rules from configuration
        loadRules();
        
        LoggerUtil.info("Reloaded rules from file.");
    }
    
    /**
     * Shuts down the rule manager
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        // Save rules
        saveRules();
        
        // Clear rules
        rules.clear();
        
        initialized = false;
        LoggerUtil.info("Shut down rule manager.");
    }
    
    /**
     * Checks if the rule manager is initialized
     * @return True if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Gets all rules
     * @return A map of all rules
     */
    public Map<String, Rule> getRules() {
        return new HashMap<>(rules);
    }
}