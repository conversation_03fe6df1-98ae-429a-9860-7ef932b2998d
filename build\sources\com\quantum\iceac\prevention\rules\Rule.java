package com.quantum.iceac.prevention.rules;

/**
 * Represents an adaptive rule for the anti-cheat system
 */
public class Rule {

    private final String name;
    private double value;
    private final double min;
    private final double max;
    private final String description;
    private long lastUpdated;
    
    /**
     * Creates a new rule
     * @param name The rule name
     * @param value The rule value
     * @param min The minimum value
     * @param max The maximum value
     * @param description The rule description
     */
    public Rule(String name, double value, double min, double max, String description) {
        this.name = name;
        this.value = value;
        this.min = min;
        this.max = max;
        this.description = description;
        this.lastUpdated = System.currentTimeMillis();
    }
    
    /**
     * Gets the rule name
     * @return The rule name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the rule value
     * @return The rule value
     */
    public double getValue() {
        return value;
    }
    
    /**
     * Sets the rule value
     * @param value The new value
     */
    public void setValue(double value) {
        // Clamp value between min and max
        this.value = Math.max(min, Math.min(max, value));
        this.lastUpdated = System.currentTimeMillis();
    }
    
    /**
     * Gets the minimum value
     * @return The minimum value
     */
    public double getMin() {
        return min;
    }
    
    /**
     * Gets the maximum value
     * @return The maximum value
     */
    public double getMax() {
        return max;
    }
    
    /**
     * Gets the rule description
     * @return The rule description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets the last updated time
     * @return The last updated time in milliseconds
     */
    public long getLastUpdated() {
        return lastUpdated;
    }
    
    /**
     * Checks if the rule is at its minimum value
     * @return True if at minimum, false otherwise
     */
    public boolean isAtMinimum() {
        return value <= min;
    }
    
    /**
     * Checks if the rule is at its maximum value
     * @return True if at maximum, false otherwise
     */
    public boolean isAtMaximum() {
        return value >= max;
    }
    
    /**
     * Gets the percentage of the value between min and max
     * @return The percentage (0-100)
     */
    public double getPercentage() {
        if (max == min) {
            return 100.0;
        }
        
        return ((value - min) / (max - min)) * 100.0;
    }
    
    /**
     * Returns a string representation of the rule
     * @return The string representation
     */
    @Override
    public String toString() {
        return name + " = " + value + " (min: " + min + ", max: " + max + ")";
    }
}