package com.quantum.iceac.metrics;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.configuration.file.FileConfiguration;

import java.io.File;

/**
 * Manages metrics and analytics for the anti-cheat system
 */
public class MetricsManager {

    private final IceAC plugin;
    private final MetricsCollector metricsCollector;
    private final AnalyticsDashboard analyticsDashboard;
    private boolean initialized = false;
    
    public MetricsManager(IceAC plugin) {
        this.plugin = plugin;
        this.metricsCollector = new MetricsCollector(plugin);
        this.analyticsDashboard = new AnalyticsDashboard(plugin);
    }
    
    /**
     * Initializes the metrics manager
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        LoggerUtil.info("Initializing Real-Time Analytics System...");
        
        // Load metrics configuration
        FileConfiguration config = plugin.getConfig();
        boolean metricsEnabled = config.getBoolean("metrics.enabled", true);
        
        if (!metricsEnabled) {
            LoggerUtil.info("Metrics collection is disabled in the configuration.");
            return;
        }
        
        // Initialize metrics collector
        metricsCollector.initialize();
        
        // Initialize analytics dashboard
        analyticsDashboard.initialize();
        
        // Create metrics directory if it doesn't exist
        File metricsDir = new File(plugin.getDataFolder(), "metrics");
        if (!metricsDir.exists()) {
            metricsDir.mkdirs();
        }
        
        initialized = true;
        LoggerUtil.info("Real-Time Analytics System initialized successfully.");
    }
    
    /**
     * Saves metrics data to disk
     */
    public void saveData() {
        if (!initialized) {
            return;
        }
        
        LoggerUtil.info("Saving metrics data...");
        
        // Save metrics data
        metricsCollector.saveData();
        
        LoggerUtil.info("Metrics data saved successfully.");
    }
    
    /**
     * Gets the metrics collector
     * @return The metrics collector
     */
    public MetricsCollector getMetricsCollector() {
        return metricsCollector;
    }
    
    /**
     * Gets the analytics dashboard
     * @return The analytics dashboard
     */
    public AnalyticsDashboard getAnalyticsDashboard() {
        return analyticsDashboard;
    }
    
    /**
     * Checks if the metrics manager is initialized
     * @return True if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}