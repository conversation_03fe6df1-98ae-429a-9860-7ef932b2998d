package com.quantum.iceac.detection.checks.packet;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.ProtocolLibrary;
import com.comphenix.protocol.events.ListenerPriority;
import com.comphenix.protocol.events.PacketAdapter;
import com.comphenix.protocol.events.PacketEvent;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for Blinker/Disabler (packet manipulation)
 */
public class BlinkerCheck extends Check {

    private static final long DEFAULT_MAX_PACKET_DELAY = 2000; // 2 seconds
    private static final int DEFAULT_MAX_QUEUED_PACKETS = 50;
    private static final long DEFAULT_BLINK_THRESHOLD = 1000; // 1 second
    
    // Track packet timing
    private final Map<UUID, Long> lastPacketTime = new HashMap<>();
    private final Map<UUID, Integer> queuedPackets = new HashMap<>();
    private final Map<UUID, Long> packetGapStart = new HashMap<>();
    private final Map<UUID, Integer> blinkerViolations = new HashMap<>();
    
    public BlinkerCheck(IceAC plugin) {
        super(plugin, "Blinker", CheckType.PACKET, "Detects packet manipulation and blinker cheats");
    }

    @Override
    public void register() {
        super.register();
        
        // Register packet listeners
        registerPacketListeners();
    }
    
    @Override
    public void unregister() {
        super.unregister();
        
        // Unregister packet listeners
        ProtocolLibrary.getProtocolManager().removePacketListeners(getPlugin());
    }
    
    /**
     * Registers packet listeners using ProtocolLib
     */
    private void registerPacketListeners() {
        // Listen to movement packets
        ProtocolLibrary.getProtocolManager().addPacketListener(
            new PacketAdapter(getPlugin(), ListenerPriority.MONITOR, 
                PacketType.Play.Client.POSITION,
                PacketType.Play.Client.POSITION_LOOK,
                PacketType.Play.Client.LOOK,
                PacketType.Play.Client.FLYING) {
                
                @Override
                public void onPacketReceiving(PacketEvent event) {
                    handleMovementPacket(event);
                }
            }
        );
        
        // Listen to interaction packets
        ProtocolLibrary.getProtocolManager().addPacketListener(
            new PacketAdapter(getPlugin(), ListenerPriority.MONITOR,
                PacketType.Play.Client.USE_ENTITY,
                PacketType.Play.Client.ARM_ANIMATION,
                PacketType.Play.Client.BLOCK_DIG,
                PacketType.Play.Client.BLOCK_PLACE) {
                
                @Override
                public void onPacketReceiving(PacketEvent event) {
                    handleInteractionPacket(event);
                }
            }
        );
    }
    
    /**
     * Handles movement packets
     * @param event The packet event
     */
    private void handleMovementPacket(PacketEvent event) {
        Player player = event.getPlayer();
        if (player == null) return;
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        
        // Check for packet gaps (blinker detection)
        checkPacketGaps(player, uuid, now);
        
        // Update last packet time
        lastPacketTime.put(uuid, now);
    }
    
    /**
     * Handles interaction packets
     * @param event The packet event
     */
    private void handleInteractionPacket(PacketEvent event) {
        Player player = event.getPlayer();
        if (player == null) return;
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        
        // Check for interaction during packet gaps
        checkInteractionDuringGap(player, uuid, now);
    }
    
    /**
     * Checks for packet gaps that indicate blinker usage
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     */
    private void checkPacketGaps(Player player, UUID uuid, long now) {
        Long lastTime = lastPacketTime.get(uuid);
        
        if (lastTime == null) {
            lastPacketTime.put(uuid, now);
            return;
        }
        
        long timeDiff = now - lastTime;
        long blinkThreshold = getPlugin().getConfig().getLong("checks.blinker.blink-threshold", DEFAULT_BLINK_THRESHOLD);
        
        // Check for packet gap start
        if (timeDiff > blinkThreshold && !packetGapStart.containsKey(uuid)) {
            packetGapStart.put(uuid, lastTime);
            LoggerUtil.debug(player.getName() + " started packet gap: " + timeDiff + "ms");
        }
        
        // Check for packet gap end
        if (packetGapStart.containsKey(uuid)) {
            long gapStart = packetGapStart.get(uuid);
            long gapDuration = now - gapStart;
            
            // If gap is over, analyze it
            if (timeDiff <= 100) { // Normal packet timing resumed
                analyzePacketGap(player, uuid, gapDuration);
                packetGapStart.remove(uuid);
            }
        }
        
        // Check for extremely long gaps
        long maxDelay = getPlugin().getConfig().getLong("checks.blinker.max-packet-delay", DEFAULT_MAX_PACKET_DELAY);
        if (timeDiff > maxDelay) {
            // Flag player for violation
            flag(player, "Packet delay too long (" + timeDiff + "ms > " + maxDelay + "ms)");
            LoggerUtil.debug(player.getName() + " failed Blinker (Long delay: " + timeDiff + "ms)");
        }
    }
    
    /**
     * Analyzes a completed packet gap
     * @param player The player
     * @param uuid The player UUID
     * @param gapDuration Duration of the gap
     */
    private void analyzePacketGap(Player player, UUID uuid, long gapDuration) {
        long blinkThreshold = getPlugin().getConfig().getLong("checks.blinker.blink-threshold", DEFAULT_BLINK_THRESHOLD);
        
        if (gapDuration > blinkThreshold) {
            int violations = blinkerViolations.getOrDefault(uuid, 0) + 1;
            blinkerViolations.put(uuid, violations);
            
            // Flag player for violation
            flag(player, "Packet gap detected (Duration: " + gapDuration + "ms, Violations: " + violations + ")");
            LoggerUtil.debug(player.getName() + " failed Blinker (Gap: " + gapDuration + "ms, Count: " + violations + ")");
            
            // Check for pattern violations
            if (violations > 3) {
                // Flag for consistent blinker usage
                flag(player, "Consistent packet gaps (Count: " + violations + ")");
                LoggerUtil.debug(player.getName() + " failed Blinker (Pattern: " + violations + " gaps)");
                
                // Reset counter
                blinkerViolations.put(uuid, 0);
            }
        }
    }
    
    /**
     * Checks for interactions during packet gaps
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     */
    private void checkInteractionDuringGap(Player player, UUID uuid, long now) {
        // Check if player is currently in a packet gap
        if (!packetGapStart.containsKey(uuid)) {
            return;
        }
        
        long gapStart = packetGapStart.get(uuid);
        long gapDuration = now - gapStart;
        
        // If player is interacting during a packet gap, it's suspicious
        if (gapDuration > 500) { // Gap longer than 500ms
            // Flag player for violation
            flag(player, "Interaction during packet gap (Gap duration: " + gapDuration + "ms)");
            LoggerUtil.debug(player.getName() + " failed Blinker (Interaction during gap: " + gapDuration + "ms)");
        }
    }
    
    /**
     * Checks for packet queue manipulation
     * @param player The player
     * @param uuid The player UUID
     */
    private void checkPacketQueue(Player player, UUID uuid) {
        int queued = queuedPackets.getOrDefault(uuid, 0);
        int maxQueued = getPlugin().getConfig().getInt("checks.blinker.max-queued-packets", DEFAULT_MAX_QUEUED_PACKETS);
        
        if (queued > maxQueued) {
            // Flag player for violation
            flag(player, "Too many queued packets (" + queued + " > " + maxQueued + ")");
            LoggerUtil.debug(player.getName() + " failed Blinker (Queued packets: " + queued + ")");
            
            // Reset queue
            queuedPackets.put(uuid, 0);
        }
    }
    
    /**
     * Checks for disabler patterns
     * @param player The player
     * @param uuid The player UUID
     */
    private void checkDisablerPatterns(Player player, UUID uuid) {
        Long lastTime = lastPacketTime.get(uuid);
        if (lastTime == null) {
            return;
        }
        
        long now = System.currentTimeMillis();
        long timeSinceLastPacket = now - lastTime;
        
        // Check for complete packet stopping (disabler)
        if (timeSinceLastPacket > 5000) { // 5 seconds without packets
            // Flag player for violation
            flag(player, "Possible disabler detected (No packets for " + (timeSinceLastPacket / 1000) + "s)");
            LoggerUtil.debug(player.getName() + " failed Blinker (Disabler: " + (timeSinceLastPacket / 1000) + "s)");
        }
    }
    
    /**
     * Checks for packet burst patterns
     * @param player The player
     * @param uuid The player UUID
     * @param packetCount Number of packets in burst
     */
    private void checkPacketBurst(Player player, UUID uuid, int packetCount) {
        if (packetCount > 20) { // More than 20 packets at once
            // Flag player for violation
            flag(player, "Packet burst detected (" + packetCount + " packets)");
            LoggerUtil.debug(player.getName() + " failed Blinker (Burst: " + packetCount + " packets)");
        }
    }
    
    @Override
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        lastPacketTime.remove(uuid);
        queuedPackets.remove(uuid);
        packetGapStart.remove(uuid);
        blinkerViolations.remove(uuid);
    }
}
