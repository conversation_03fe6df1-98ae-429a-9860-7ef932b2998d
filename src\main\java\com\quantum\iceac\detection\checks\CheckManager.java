package com.quantum.iceac.detection.checks;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.combat.AimCheck;
import com.quantum.iceac.detection.checks.combat.AutoClickerCheck;
import com.quantum.iceac.detection.checks.combat.KillAuraCheck;
import com.quantum.iceac.detection.checks.combat.ReachCheck;
import com.quantum.iceac.detection.checks.combat.VelocityCheck;
import com.quantum.iceac.detection.checks.movement.FlyCheck;
import com.quantum.iceac.detection.checks.movement.InventoryMoveCheck;
import com.quantum.iceac.detection.checks.movement.ScaffoldCheck;
import com.quantum.iceac.detection.checks.movement.SpeedCheck;
import com.quantum.iceac.detection.checks.packet.BlinkerCheck;
import com.quantum.iceac.detection.checks.packet.PacketCheck;
import com.quantum.iceac.utils.LoggerUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Manages all cheat detection checks
 */
public class CheckManager {

    private final IceAC plugin;
    private final List<Check> checks;
    
    public CheckManager(IceAC plugin) {
        this.plugin = plugin;
        this.checks = new ArrayList<>();
    }
    
    /**
     * Registers all checks
     */
    public void registerChecks() {
        // Check if detection is enabled
        if (!plugin.getConfig().getBoolean("detection.enabled", true)) {
            LoggerUtil.info("Detection engine is disabled in the configuration.");
            return;
        }
        
        // Register combat checks
        registerCheck(new AimCheck(plugin));
        registerCheck(new AutoClickerCheck(plugin));
        registerCheck(new KillAuraCheck(plugin));
        registerCheck(new ReachCheck(plugin));
        registerCheck(new VelocityCheck(plugin));
        
        // Register movement checks
        registerCheck(new FlyCheck(plugin));
        registerCheck(new InventoryMoveCheck(plugin));
        registerCheck(new ScaffoldCheck(plugin));
        registerCheck(new SpeedCheck(plugin));
        
        // Register packet checks
        registerCheck(new BlinkerCheck(plugin));
        registerCheck(new PacketCheck(plugin));
        
        LoggerUtil.info("Registered " + checks.size() + " checks.");
    }
    
    /**
     * Registers a check
     * @param check The check to register
     */
    private void registerCheck(Check check) {
        // Check if the check is enabled in the configuration
        String checkName = check.getName().toLowerCase();
        if (!plugin.getConfig().getBoolean("checks." + checkName + ".enabled", true)) {
            LoggerUtil.debug("Check " + check.getName() + " is disabled in the configuration.");
            return;
        }
        
        // Register the check
        checks.add(check);
        check.register();
        LoggerUtil.debug("Registered check: " + check.getName());
    }
    
    /**
     * Unregisters all checks
     */
    public void unregisterChecks() {
        for (Check check : checks) {
            check.unregister();
            LoggerUtil.debug("Unregistered check: " + check.getName());
        }
        
        checks.clear();
        LoggerUtil.info("Unregistered all checks.");
    }
    
    /**
     * Gets all registered checks
     * @return A list of all registered checks
     */
    public List<Check> getChecks() {
        return new ArrayList<>(checks);
    }
    
    /**
     * Gets a check by name
     * @param name The name of the check
     * @return The check, or null if not found
     */
    public Check getCheck(String name) {
        for (Check check : checks) {
            if (check.getName().equalsIgnoreCase(name)) {
                return check;
            }
        }
        return null;
    }
}