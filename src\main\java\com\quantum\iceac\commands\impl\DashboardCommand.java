package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command to toggle the analytics dashboard
 */
public class DashboardCommand implements SubCommand {

    private final IceAC plugin;
    
    public DashboardCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "dashboard";
    }
    
    @Override
    public String getDescription() {
        return "Toggles the analytics dashboard";
    }
    
    @Override
    public String getUsage() {
        return "/iceac dashboard [on|off]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("dash", "analytics");
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(MessageUtil.getMessage("general.player-only"));
            return true;
        }
        
        Player player = (Player) sender;
        
        // Get analytics dashboard
        if (plugin.getMetricsManager() == null || 
            plugin.getMetricsManager().getAnalyticsDashboard() == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cAnalytics dashboard is not initialized.");
            return true;
        }
        
        // Toggle dashboard
        boolean enabled;
        
        if (args.length > 0) {
            if (args[0].equalsIgnoreCase("on")) {
                enabled = true;
            } else if (args[0].equalsIgnoreCase("off")) {
                enabled = false;
            } else {
                // Invalid argument, toggle current state
                enabled = !plugin.getMetricsManager().getAnalyticsDashboard().hasDashboardEnabled(player);
            }
        } else {
            // No argument, toggle current state
            enabled = !plugin.getMetricsManager().getAnalyticsDashboard().hasDashboardEnabled(player);
        }
        
        // Set dashboard state
        plugin.getMetricsManager().getAnalyticsDashboard().setDashboardEnabled(player, enabled);
        
        // Send message
        if (enabled) {
            sender.sendMessage(MessageUtil.getMessage("dashboard.enabled"));
            // Send dashboard immediately
            plugin.getMetricsManager().getAnalyticsDashboard().sendDashboard(player);
        } else {
            sender.sendMessage(MessageUtil.getMessage("dashboard.disabled"));
        }
        
        return true;
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (args.length == 1) {
            List<String> completions = new ArrayList<>();
            completions.add("on");
            completions.add("off");
            return completions;
        }
        
        return new ArrayList<>();
    }
}