package com.quantum.iceac.prevention;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.prevention.filters.PacketFilterManager;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;

/**
 * Smart prevention system for the anti-cheat
 * Handles the prevention of cheats and exploits
 */
public class PreventionSystem {

    private final IceAC plugin;
    private PacketFilterManager packetFilterManager;
    private RuleManager ruleManager;
    private boolean initialized = false;
    
    public PreventionSystem(IceAC plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Initializes the prevention system
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        LoggerUtil.info("Initializing Smart Prevention System...");
        
        // Initialize packet filter manager
        packetFilterManager = new PacketFilterManager(plugin);
        packetFilterManager.initialize();
        
        // Initialize rule manager
        ruleManager = new RuleManager(plugin);
        ruleManager.loadRules();
        
        initialized = true;
        LoggerUtil.info("Smart Prevention System initialized successfully.");
    }
    
    /**
     * Shuts down the prevention system
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        LoggerUtil.info("Shutting down Smart Prevention System...");
        
        // Shutdown packet filter manager
        if (packetFilterManager != null) {
            packetFilterManager.shutdown();
        }
        
        // Save rules
        if (ruleManager != null) {
            ruleManager.saveRules();
        }
        
        initialized = false;
        LoggerUtil.info("Smart Prevention System shut down successfully.");
    }
    
    /**
     * Gets the packet filter manager
     * @return The packet filter manager
     */
    public PacketFilterManager getPacketFilterManager() {
        return packetFilterManager;
    }
    
    /**
     * Gets the rule manager
     * @return The rule manager
     */
    public RuleManager getRuleManager() {
        return ruleManager;
    }
    
    /**
     * Checks if the prevention system is initialized
     * @return True if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}