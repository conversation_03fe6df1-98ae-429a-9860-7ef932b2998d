package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckManager;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Command to manually add violations to players for testing purposes
 */
public class AddViolationCommand implements SubCommand {

    private final IceAC plugin;
    
    public AddViolationCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "add";
    }
    
    @Override
    public String getDescription() {
        return "Manually adds violations to a player for testing purposes";
    }
    
    @Override
    public String getUsage() {
        return "/iceac add <player> <check> [amount] [reason]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        List<String> aliases = new ArrayList<>();
        aliases.add("addvl");
        aliases.add("violation");
        return aliases;
    }
    
    @Override
    public void execute(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", getUsage()));
            return;
        }
        
        // Get player
        String playerName = args[0];
        Player player = Bukkit.getPlayer(playerName);
        
        if (player == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cPlayer not found: " + playerName);
            return;
        }
        
        // Get check
        String checkName = args[1];
        CheckManager checkManager = plugin.getDetectionEngine().getCheckManager();
        
        if (checkManager == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cCheck manager is not initialized.");
            return;
        }
        
        Check check = checkManager.getCheck(checkName);
        
        if (check == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cCheck not found: " + checkName);
            return;
        }
        
        // Get amount (optional)
        int amount = 1;
        if (args.length >= 3) {
            try {
                amount = Integer.parseInt(args[2]);
                if (amount <= 0) {
                    sender.sendMessage(MessageUtil.getPrefix() + "§cAmount must be a positive number.");
                    return;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage(MessageUtil.getPrefix() + "§cInvalid amount: " + args[2]);
                return;
            }
        }
        
        // Get reason (optional)
        String reason = "Manual violation";
        if (args.length >= 4) {
            reason = String.join(" ", Arrays.copyOfRange(args, 3, args.length));
        }
        
        // Add violations
        PlayerData playerData = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
        if (playerData == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cPlayer data not found for " + playerName);
            return;
        }
        
        // Temporarily disable exemption if player is exempt
        boolean wasExempt = playerData.isExempt();
        if (wasExempt) {
            playerData.setExempt(false);
        }
        
        // Add violations
        int totalViolations = 0;
        for (int i = 0; i < amount; i++) {
            totalViolations = check.flag(player, reason + " (Manual)");
        }
        
        // Restore exemption status
        if (wasExempt) {
            playerData.setExempt(true);
        }
        
        // Send confirmation message
        sender.sendMessage(MessageUtil.getPrefix() + "§aAdded " + amount + " violation(s) to " + 
                player.getName() + " for check " + check.getName() + ". Total: " + totalViolations);
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // Tab complete player names
            String partialName = args[0].toLowerCase();
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.getName().toLowerCase().startsWith(partialName)) {
                    completions.add(player.getName());
                }
            }
        } else if (args.length == 2) {
            // Tab complete check names
            String partialCheck = args[1].toLowerCase();
            CheckManager checkManager = plugin.getDetectionEngine().getCheckManager();
            
            if (checkManager != null) {
                completions.addAll(checkManager.getChecks().stream()
                        .map(Check::getName)
                        .filter(name -> name.toLowerCase().startsWith(partialCheck))
                        .collect(Collectors.toList()));
            }
        } else if (args.length == 3) {
            // Tab complete amount
            completions.add("1");
            completions.add("5");
            completions.add("10");
        }
        
        return completions;
    }
}