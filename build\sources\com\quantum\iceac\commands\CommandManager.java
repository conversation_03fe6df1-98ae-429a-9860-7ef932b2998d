package com.quantum.iceac.commands;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.impl.AddViolationCommand;
import com.quantum.iceac.commands.subcommands.*;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manages all commands for the plugin
 */
public class CommandManager implements CommandExecutor, TabCompleter {

    private final IceAC plugin;
    private final Map<String, SubCommand> subCommands;
    
    public CommandManager(IceAC plugin) {
        this.plugin = plugin;
        this.subCommands = new HashMap<>();
    }
    
    /**
     * Registers all commands
     */
    public void registerCommands() {
        // Register main command
        plugin.getCommand("iceac").setExecutor(this);
        plugin.getCommand("iceac").setTabCompleter(this);
        
        // Register subcommands
        registerSubCommand(new HelpCommand(plugin));
        registerSubCommand(new ReloadCommand(plugin));
        registerSubCommand(new StatusCommand(plugin));
        registerSubCommand(new AlertsCommand(plugin));
        registerSubCommand(new DebugCommand(plugin));
        registerSubCommand(new WhitelistCommand(plugin));
        registerSubCommand(new AddViolationCommand(plugin));
        
        LoggerUtil.info("Registered " + subCommands.size() + " commands.");
    }
    
    /**
     * Registers a subcommand
     * @param subCommand The subcommand to register
     */
    private void registerSubCommand(SubCommand subCommand) {
        subCommands.put(subCommand.getName().toLowerCase(), subCommand);
        for (String alias : subCommand.getAliases()) {
            subCommands.put(alias.toLowerCase(), subCommand);
        }
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            // No subcommand specified, show help
            subCommands.get("help").execute(sender, args);
            return true;
        }
        
        String subCommandName = args[0].toLowerCase();
        if (subCommands.containsKey(subCommandName)) {
            SubCommand subCommand = subCommands.get(subCommandName);
            
            // Check permission
            if (subCommand.getPermission() != null && !sender.hasPermission(subCommand.getPermission())) {
                sender.sendMessage("§cYou don't have permission to use this command.");
                return true;
            }
            
            // Execute subcommand
            String[] subArgs = new String[args.length - 1];
            System.arraycopy(args, 1, subArgs, 0, args.length - 1);
            subCommand.execute(sender, subArgs);
        } else {
            sender.sendMessage("§cUnknown subcommand. Type /iceac help for a list of commands.");
        }
        
        return true;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            // Tab complete subcommands
            String partialCommand = args[0].toLowerCase();
            for (SubCommand subCommand : getUniqueSubCommands()) {
                if (subCommand.getName().startsWith(partialCommand) && 
                    (subCommand.getPermission() == null || sender.hasPermission(subCommand.getPermission()))) {
                    completions.add(subCommand.getName());
                }
            }
        } else if (args.length > 1) {
            // Tab complete subcommand arguments
            String subCommandName = args[0].toLowerCase();
            if (subCommands.containsKey(subCommandName)) {
                SubCommand subCommand = subCommands.get(subCommandName);
                
                // Check permission
                if (subCommand.getPermission() == null || sender.hasPermission(subCommand.getPermission())) {
                    String[] subArgs = new String[args.length - 1];
                    System.arraycopy(args, 1, subArgs, 0, args.length - 1);
                    completions.addAll(subCommand.tabComplete(sender, subArgs));
                }
            }
        }
        
        return completions;
    }
    
    /**
     * Gets a list of unique subcommands (no aliases)
     * @return A list of unique subcommands
     */
    private List<SubCommand> getUniqueSubCommands() {
        List<SubCommand> uniqueCommands = new ArrayList<>();
        for (SubCommand subCommand : subCommands.values()) {
            if (!uniqueCommands.contains(subCommand)) {
                uniqueCommands.add(subCommand);
            }
        }
        return uniqueCommands;
    }
}