package com.quantum.iceac;

import com.quantum.iceac.commands.CommandManager;
import com.quantum.iceac.config.ConfigManager;
import com.quantum.iceac.detection.DetectionEngine;
import com.quantum.iceac.listeners.PlayerListener;
import com.quantum.iceac.metrics.MetricsManager;
import com.quantum.iceac.prevention.PreventionSystem;
import com.quantum.iceac.utils.LoggerUtil;
import org.bstats.bukkit.Metrics;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * IceAC - Revolutionary Anti-Cheat Protection for Minecraft servers
 * Main plugin class that serves as the entry point for the plugin
 */
public class IceAC extends JavaPlugin {

    private static IceAC instance;
    private ConfigManager configManager;
    private DetectionEngine detectionEngine;
    private PreventionSystem preventionSystem;
    private MetricsManager metricsManager;
    private CommandManager commandManager;

    @Override
    public void onEnable() {
        instance = this;
        
        // Initialize logger
        LoggerUtil.init(this);
        LoggerUtil.info("Initializing IceAC Anti-Cheat Protection...");
        
        // Check dependencies
        if (!checkDependencies()) {
            LoggerUtil.severe("Required dependencies not found. Disabling plugin.");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        // Initialize components
        initializeComponents();
        
        // Register event listeners
        registerListeners();
        
        // Register commands
        commandManager = new CommandManager(this);
        commandManager.registerCommands();
        
        // Setup metrics
        setupMetrics();
        
        LoggerUtil.info("IceAC has been successfully enabled!");
        LoggerUtil.info("Running on version " + getDescription().getVersion());
    }

    @Override
    public void onDisable() {
        LoggerUtil.info("Shutting down IceAC...");
        
        // Cleanup and save data
        if (configManager != null) {
            configManager.saveAll();
        }
        
        if (detectionEngine != null) {
            detectionEngine.shutdown();
        }
        
        if (preventionSystem != null) {
            preventionSystem.shutdown();
        }
        
        if (metricsManager != null) {
            metricsManager.shutdown();
        }
        
        LoggerUtil.info("IceAC has been successfully disabled!");
    }
    
    private boolean checkDependencies() {
        if (Bukkit.getPluginManager().getPlugin("ProtocolLib") == null) {
            LoggerUtil.severe("ProtocolLib is required for IceAC to function properly!");
            return false;
        }
        return true;
    }
    
    private void initializeComponents() {
        // Initialize configuration
        configManager = new ConfigManager(this);
        configManager.loadConfigurations();
        
        // Initialize detection engine
        detectionEngine = new DetectionEngine(this);
        detectionEngine.initialize();
        
        // Initialize prevention system
        preventionSystem = new PreventionSystem(this);
        preventionSystem.initialize();
        
        // Initialize metrics manager
        metricsManager = new MetricsManager(this);
        metricsManager.initialize();
    }
    
    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
    }
    
    private void setupMetrics() {
        // Initialize bStats metrics
        int pluginId = 12345; // Replace with actual bStats plugin ID
        Metrics metrics = new Metrics(this, pluginId);
    }
    
    /**
     * Get the plugin instance
     * @return The plugin instance
     */
    public static IceAC getInstance() {
        return instance;
    }
    
    /**
     * Get the configuration manager
     * @return The configuration manager
     */
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    /**
     * Get the detection engine
     * @return The detection engine
     */
    public DetectionEngine getDetectionEngine() {
        return detectionEngine;
    }
    
    /**
     * Get the prevention system
     * @return The prevention system
     */
    public PreventionSystem getPreventionSystem() {
        return preventionSystem;
    }
    
    /**
     * Get the metrics manager
     * @return The metrics manager
     */
    public MetricsManager getMetricsManager() {
        return metricsManager;
    }
}