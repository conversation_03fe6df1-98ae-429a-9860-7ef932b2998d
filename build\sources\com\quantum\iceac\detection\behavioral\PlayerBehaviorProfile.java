package com.quantum.iceac.detection.behavioral;

import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Stores behavioral analysis data for a player
 */
public class PlayerBehaviorProfile {

    private final UUID playerUUID;
    private final String playerName;
    private final long creationTime;
    private final List<BehaviorEvent> events;
    
    // Movement behavior metrics
    private double movementConsistency = 0.0;
    private double averageDirectionChanges = 0.0;
    private double speedVariation = 0.0;
    
    // Combat behavior metrics
    private double aimConsistency = 0.0;
    private double attackTimingVariation = 0.0;
    private double hitAccuracy = 0.0;
    
    // Timing behavior metrics
    private double averageReactionTime = 0.0;
    private double inputTimingConsistency = 0.0;
    
    // Pattern analysis metrics
    private double patternRepetition = 0.0;
    private double precisionScore = 0.0;
    
    // Statistical data
    private int totalEvents = 0;
    private long lastEventTime = 0;
    private double overallSuspicionScore = 0.0;
    
    /**
     * Creates a new player behavior profile
     * @param player The player
     */
    public PlayerBehaviorProfile(Player player) {
        this.playerUUID = player.getUniqueId();
        this.playerName = player.getName();
        this.creationTime = System.currentTimeMillis();
        this.events = new ArrayList<>();
    }
    
    /**
     * Adds a behavior event to the profile
     * @param event The behavior event
     */
    public void addEvent(BehaviorEvent event) {
        events.add(event);
        totalEvents++;
        lastEventTime = event.getTimestamp();
        
        // Limit event history
        if (events.size() > 500) {
            events.remove(0);
        }
    }
    
    /**
     * Gets the player UUID
     * @return The player UUID
     */
    public UUID getPlayerUUID() {
        return playerUUID;
    }
    
    /**
     * Gets the player name
     * @return The player name
     */
    public String getPlayerName() {
        return playerName;
    }
    
    /**
     * Gets the profile creation time
     * @return The creation time
     */
    public long getCreationTime() {
        return creationTime;
    }
    
    /**
     * Gets all behavior events
     * @return List of behavior events
     */
    public List<BehaviorEvent> getEvents() {
        return new ArrayList<>(events);
    }
    
    /**
     * Gets events of a specific type
     * @param type The event type
     * @return List of events of the specified type
     */
    public List<BehaviorEvent> getEventsByType(BehaviorEventType type) {
        return events.stream()
                .filter(event -> event.getType() == type)
                .toList();
    }
    
    /**
     * Gets recent events within the specified time frame
     * @param timeFrame Time frame in milliseconds
     * @return List of recent events
     */
    public List<BehaviorEvent> getRecentEvents(long timeFrame) {
        long cutoff = System.currentTimeMillis() - timeFrame;
        return events.stream()
                .filter(event -> event.getTimestamp() > cutoff)
                .toList();
    }
    
    // Movement behavior getters and setters
    
    public double getMovementConsistency() {
        return movementConsistency;
    }
    
    public void setMovementConsistency(double movementConsistency) {
        this.movementConsistency = movementConsistency;
    }
    
    public double getAverageDirectionChanges() {
        return averageDirectionChanges;
    }
    
    public void setAverageDirectionChanges(double averageDirectionChanges) {
        this.averageDirectionChanges = averageDirectionChanges;
    }
    
    public double getSpeedVariation() {
        return speedVariation;
    }
    
    public void setSpeedVariation(double speedVariation) {
        this.speedVariation = speedVariation;
    }
    
    // Combat behavior getters and setters
    
    public double getAimConsistency() {
        return aimConsistency;
    }
    
    public void setAimConsistency(double aimConsistency) {
        this.aimConsistency = aimConsistency;
    }
    
    public double getAttackTimingVariation() {
        return attackTimingVariation;
    }
    
    public void setAttackTimingVariation(double attackTimingVariation) {
        this.attackTimingVariation = attackTimingVariation;
    }
    
    public double getHitAccuracy() {
        return hitAccuracy;
    }
    
    public void setHitAccuracy(double hitAccuracy) {
        this.hitAccuracy = hitAccuracy;
    }
    
    // Timing behavior getters and setters
    
    public double getAverageReactionTime() {
        return averageReactionTime;
    }
    
    public void setAverageReactionTime(double averageReactionTime) {
        this.averageReactionTime = averageReactionTime;
    }
    
    public double getInputTimingConsistency() {
        return inputTimingConsistency;
    }
    
    public void setInputTimingConsistency(double inputTimingConsistency) {
        this.inputTimingConsistency = inputTimingConsistency;
    }
    
    // Pattern analysis getters and setters
    
    public double getPatternRepetition() {
        return patternRepetition;
    }
    
    public void setPatternRepetition(double patternRepetition) {
        this.patternRepetition = patternRepetition;
    }
    
    public double getPrecisionScore() {
        return precisionScore;
    }
    
    public void setPrecisionScore(double precisionScore) {
        this.precisionScore = precisionScore;
    }
    
    // Statistical data getters and setters
    
    public int getTotalEvents() {
        return totalEvents;
    }
    
    public long getLastEventTime() {
        return lastEventTime;
    }
    
    public double getOverallSuspicionScore() {
        return overallSuspicionScore;
    }
    
    public void setOverallSuspicionScore(double overallSuspicionScore) {
        this.overallSuspicionScore = overallSuspicionScore;
    }
    
    /**
     * Gets the profile age in milliseconds
     * @return The profile age
     */
    public long getProfileAge() {
        return System.currentTimeMillis() - creationTime;
    }
    
    /**
     * Gets the time since last event in milliseconds
     * @return Time since last event
     */
    public long getTimeSinceLastEvent() {
        return lastEventTime > 0 ? System.currentTimeMillis() - lastEventTime : 0;
    }
    
    /**
     * Calculates the event rate (events per minute)
     * @return Events per minute
     */
    public double getEventRate() {
        long profileAgeMinutes = getProfileAge() / 60000;
        return profileAgeMinutes > 0 ? (double) totalEvents / profileAgeMinutes : 0.0;
    }
    
    /**
     * Gets a summary of the behavior profile
     * @return Profile summary string
     */
    public String getSummary() {
        return String.format(
                "PlayerBehaviorProfile{player=%s, age=%dmin, events=%d, suspicion=%.3f, " +
                "movement=%.3f, combat=%.3f, timing=%.3f}",
                playerName,
                getProfileAge() / 60000,
                totalEvents,
                overallSuspicionScore,
                movementConsistency,
                aimConsistency,
                inputTimingConsistency
        );
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
