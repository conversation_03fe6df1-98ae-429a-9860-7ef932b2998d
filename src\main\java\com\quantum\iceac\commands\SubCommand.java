package com.quantum.iceac.commands;

import org.bukkit.command.CommandSender;

import java.util.List;

/**
 * Interface for all subcommands
 */
public interface SubCommand {

    /**
     * Gets the name of the subcommand
     * @return The name of the subcommand
     */
    String getName();
    
    /**
     * Gets the description of the subcommand
     * @return The description of the subcommand
     */
    String getDescription();
    
    /**
     * Gets the usage of the subcommand
     * @return The usage of the subcommand
     */
    String getUsage();
    
    /**
     * Gets the permission required to use the subcommand
     * @return The permission required to use the subcommand
     */
    String getPermission();
    
    /**
     * Gets the aliases of the subcommand
     * @return The aliases of the subcommand
     */
    List<String> getAliases();
    
    /**
     * Executes the subcommand
     * @param sender The sender of the command
     * @param args The arguments of the command
     * @return True if the command was executed successfully
     */
    boolean execute(CommandSender sender, String[] args);
    
    /**
     * Tab completes the subcommand
     * @param sender The sender of the command
     * @param args The arguments of the command
     * @return A list of tab completions
     */
    List<String> tabComplete(CommandSender sender, String[] args);
}