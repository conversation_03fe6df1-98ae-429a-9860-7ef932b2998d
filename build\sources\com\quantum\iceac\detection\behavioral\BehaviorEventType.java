package com.quantum.iceac.detection.behavioral;

/**
 * Types of behavior events that can be tracked
 */
public enum BehaviorEventType {
    
    /**
     * Movement-related events
     */
    MOVEMENT("Movement", "Player movement and positioning"),
    
    /**
     * Combat-related events
     */
    COMBAT("Combat", "Player combat actions and targeting"),
    
    /**
     * Interaction events
     */
    INTERACTION("Interaction", "Player interactions with blocks and entities"),
    
    /**
     * Packet-related events
     */
    PACKET("Packet", "Network packet patterns and timing"),
    
    /**
     * Input timing events
     */
    INPUT_TIMING("Input Timing", "Player input patterns and timing"),
    
    /**
     * Violation events
     */
    VIOLATION("Violation", "Anti-cheat violation occurrences"),
    
    /**
     * Session events
     */
    SESSION("Session", "Player session and connection events"),
    
    /**
     * Performance events
     */
    PERFORMANCE("Performance", "Player performance metrics");
    
    private final String name;
    private final String description;
    
    BehaviorEventType(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    /**
     * Gets the event type name
     * @return The name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the event type description
     * @return The description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets an event type by name
     * @param name The name to search for
     * @return The event type, or null if not found
     */
    public static BehaviorEventType getByName(String name) {
        for (BehaviorEventType type : values()) {
            if (type.getName().equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
}
