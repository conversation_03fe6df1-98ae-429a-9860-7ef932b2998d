package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckManager;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command to manage checks
 */
public class CheckCommand implements SubCommand {

    private final IceAC plugin;
    
    public CheckCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "check";
    }
    
    @Override
    public String getDescription() {
        return "Manages individual checks";
    }
    
    @Override
    public String getUsage() {
        return "/iceac check <list|info|enable|disable> [check]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("checks", "c");
    }
    
    @Override
    public boolean execute(<PERSON><PERSON>ender sender, String[] args) {
        if (args.length == 0) {
            sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", getUsage()));
            return true;
        }
        
        String action = args[0].toLowerCase();
        CheckManager checkManager = plugin.getDetectionEngine().getCheckManager();
        
        if (checkManager == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cCheck manager is not initialized.");
            return true;
        }
        
        if (action.equals("list")) {
            // List all checks
            listChecks(sender, checkManager);
            return true;
        } else if (action.equals("info")) {
            // Show check info
            if (args.length < 2) {
                sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", "/iceac check info <check>"));
                return true;
            }
            
            String checkName = args[1];
            showCheckInfo(sender, checkManager, checkName);
            return true;
        } else if (action.equals("enable")) {
            // Enable check
            if (args.length < 2) {
                sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", "/iceac check enable <check>"));
                return true;
            }
            
            String checkName = args[1];
            enableCheck(sender, checkManager, checkName, true);
            return true;
        } else if (action.equals("disable")) {
            // Disable check
            if (args.length < 2) {
                sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", "/iceac check disable <check>"));
                return true;
            }
            
            String checkName = args[1];
            enableCheck(sender, checkManager, checkName, false);
            return true;
        } else {
            sender.sendMessage(MessageUtil.getMessage("errors.command-usage", "%usage%", getUsage()));
            return true;
        }
    }
    
    /**
     * Lists all checks
     * @param sender The command sender
     * @param checkManager The check manager
     */
    private void listChecks(CommandSender sender, CheckManager checkManager) {
        List<Check> checks = checkManager.getAllChecks();
        
        sender.sendMessage(MessageUtil.getMessage("commands.check-list-header"));
        
        for (Check check : checks) {
            String status = check.isEnabled() ? "§aEnabled" : "§cDisabled";
            sender.sendMessage(MessageUtil.getMessage("commands.check-list-format", 
                    "%check%", check.getName(), 
                    "%status%", status, 
                    "%description%", check.getDescription()));
        }
        
        sender.sendMessage(MessageUtil.getMessage("commands.check-list-footer"));
    }
    
    /**
     * Shows information about a check
     * @param sender The command sender
     * @param checkManager The check manager
     * @param checkName The check name
     */
    private void showCheckInfo(CommandSender sender, CheckManager checkManager, String checkName) {
        Check check = checkManager.getCheck(checkName);
        
        if (check == null) {
            sender.sendMessage(MessageUtil.getMessage("errors.check-not-found", "%check%", checkName));
            return;
        }
        
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-header", "%check%", check.getName()));
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-type", "%type%", check.getType().getName()));
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-description", "%description%", check.getDescription()));
        
        String status = check.isEnabled() ? "§aEnabled" : "§cDisabled";
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-status", "%status%", status));
        
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-violations", "%violations%", String.valueOf(check.getViolationCount())));
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-punishments", "%punishments%", String.valueOf(check.getPunishmentCount())));
        
        sender.sendMessage(MessageUtil.getMessage("commands.check-info-footer"));
    }
    
    /**
     * Enables or disables a check
     * @param sender The command sender
     * @param checkManager The check manager
     * @param checkName The check name
     * @param enable Whether to enable or disable the check
     */
    private void enableCheck(CommandSender sender, CheckManager checkManager, String checkName, boolean enable) {
        Check check = checkManager.getCheck(checkName);
        
        if (check == null) {
            sender.sendMessage(MessageUtil.getMessage("errors.check-not-found", "%check%", checkName));
            return;
        }
        
        // Set check state
        check.setEnabled(enable);
        
        // Save to config
        String configPath = "checks." + check.getType().getName().toLowerCase() + "." + check.getName().toLowerCase() + ".enabled";
        plugin.getConfig().set(configPath, enable);
        plugin.saveConfig();
        
        // Send message
        if (enable) {
            sender.sendMessage(MessageUtil.getMessage("commands.check-enable", "%check%", check.getName()));
        } else {
            sender.sendMessage(MessageUtil.getMessage("commands.check-disable", "%check%", check.getName()));
        }
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            completions.add("list");
            completions.add("info");
            completions.add("enable");
            completions.add("disable");
        } else if (args.length == 2) {
            String action = args[0].toLowerCase();
            
            if (action.equals("info") || action.equals("enable") || action.equals("disable")) {
                CheckManager checkManager = plugin.getDetectionEngine().getCheckManager();
                
                if (checkManager != null) {
                    for (Check check : checkManager.getAllChecks()) {
                        completions.add(check.getName());
                    }
                }
            }
        }
        
        return completions;
    }
}