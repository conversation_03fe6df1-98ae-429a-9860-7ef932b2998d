package com.quantum.iceac.detection.checks;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.utils.LoggerUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Manages all cheat detection checks
 */
public class CheckManager {

    private final IceAC plugin;
    private final List<Check> checks;
    
    public CheckManager(IceAC plugin) {
        this.plugin = plugin;
        this.checks = new ArrayList<>();
    }
    
    /**
     * Registers all checks
     */
    public void registerChecks() {
        // Check if detection is enabled
        if (!plugin.getConfig().getBoolean("detection.enabled", true)) {
            LoggerUtil.info("Detection engine is disabled in the configuration.");
            return;
        }
        
        // Register basic checks only for minimal build
        // TODO: Re-enable other checks after fixing constructor issues

        // registerCheck(new AimCheck(plugin));
        // registerCheck(new AutoClickerCheck(plugin));
        // registerCheck(new CriticalsCheck(plugin));
        // registerCheck(new KillAuraCheck(plugin));
        // registerCheck(new ReachCheck(plugin));
        // registerCheck(new VelocityCheck(plugin));

        // registerCheck(new FlyCheck(plugin));
        // registerCheck(new InventoryMoveCheck(plugin));
        // registerCheck(new JesusCheck(plugin));
        // registerCheck(new NoFallCheck(plugin));
        // registerCheck(new ScaffoldCheck(plugin));
        // registerCheck(new SpeedCheck(plugin));
        // registerCheck(new StepCheck(plugin));

        // registerCheck(new BadPacketsCheck(plugin));
        // registerCheck(new BlinkerCheck(plugin));
        // registerCheck(new PacketCheck(plugin));
        // registerCheck(new PingSpoofCheck(plugin));
        // registerCheck(new TimerCheck(plugin));
        
        LoggerUtil.info("Registered " + checks.size() + " checks.");
    }
    
    /**
     * Registers a check
     * @param check The check to register
     */
    private void registerCheck(Check check) {
        // Check if the check is enabled in the configuration
        String checkName = check.getName().toLowerCase();
        if (!plugin.getConfig().getBoolean("checks." + checkName + ".enabled", true)) {
            LoggerUtil.debug("Check " + check.getName() + " is disabled in the configuration.");
            return;
        }
        
        // Register the check
        checks.add(check);
        check.register();
        LoggerUtil.debug("Registered check: " + check.getName());
    }
    
    /**
     * Unregisters all checks
     */
    public void unregisterChecks() {
        for (Check check : checks) {
            check.unregister();
            LoggerUtil.debug("Unregistered check: " + check.getName());
        }
        
        checks.clear();
        LoggerUtil.info("Unregistered all checks.");
    }
    
    /**
     * Gets all registered checks
     * @return A list of all registered checks
     */
    public List<Check> getChecks() {
        return new ArrayList<>(checks);
    }

    /**
     * Gets all registered checks (alias for getChecks)
     * @return A list of all registered checks
     */
    public List<Check> getAllChecks() {
        return getChecks();
    }
    
    /**
     * Gets a check by name
     * @param name The name of the check
     * @return The check, or null if not found
     */
    public Check getCheck(String name) {
        for (Check check : checks) {
            if (check.getName().equalsIgnoreCase(name)) {
                return check;
            }
        }
        return null;
    }
}