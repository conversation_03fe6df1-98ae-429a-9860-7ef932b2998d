package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerMoveEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for Scaffold (automatic block placement while moving)
 */
public class ScaffoldCheck extends Check {

    private static final double DEFAULT_MAX_SCAFFOLD_SPEED = 0.2;
    private static final int DEFAULT_MAX_BLOCKS_PER_SECOND = 10;
    private static final double DEFAULT_MIN_ROTATION_CHANGE = 5.0;
    
    // Track scaffold patterns
    private final Map<UUID, Integer> blockPlaceCount = new HashMap<>();
    private final Map<UUID, Long> lastBlockPlaceTime = new HashMap<>();
    private final Map<UUID, Location> lastBlockPlace = new HashMap<>();
    private final Map<UUID, Float> lastYaw = new HashMap<>();
    private final Map<UUID, Float> lastPitch = new HashMap<>();
    private final Map<UUID, Long> lastRotationTime = new HashMap<>();
    
    public ScaffoldCheck(IceAC plugin) {
        super(plugin, "Scaffold", CheckType.MOVEMENT, "Detects automatic block placement while moving");
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockPlace(BlockPlaceEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative mode
        if (player.getGameMode() == GameMode.CREATIVE) {
            return;
        }
        
        // Skip if player is sneaking (legitimate scaffolding)
        if (player.isSneaking()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        Block block = event.getBlock();
        
        // Check block placement rate
        checkBlockPlacementRate(player, uuid, now);
        
        // Check scaffold pattern
        checkScaffoldPattern(player, uuid, block, now);
        
        // Check rotation consistency
        checkRotationConsistency(player, uuid, now);
        
        // Update tracking data
        lastBlockPlace.put(uuid, block.getLocation());
        lastBlockPlaceTime.put(uuid, now);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Check if player is moving while placing blocks
        checkMovementWhileScaffolding(player, event);
    }
    
    /**
     * Checks block placement rate
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     */
    private void checkBlockPlacementRate(Player player, UUID uuid, long now) {
        // Initialize or reset counter if needed
        if (!lastBlockPlaceTime.containsKey(uuid) || now - lastBlockPlaceTime.get(uuid) > 1000) {
            blockPlaceCount.put(uuid, 1);
            return;
        }
        
        // Increment counter
        int count = blockPlaceCount.get(uuid) + 1;
        blockPlaceCount.put(uuid, count);
        
        // Check if within the same second
        if (now - lastBlockPlaceTime.get(uuid) < 1000) {
            int maxBlocks = getPlugin().getConfig().getInt("checks.scaffold.max-blocks-per-second", DEFAULT_MAX_BLOCKS_PER_SECOND);
            
            if (count > maxBlocks) {
                // Flag player for violation
                flag(player, "Placing blocks too fast (" + count + " > " + maxBlocks + " blocks/sec)");
                LoggerUtil.debug(player.getName() + " failed Scaffold (Block rate: " + count + " > " + maxBlocks + ")");
            }
        }
    }
    
    /**
     * Checks scaffold pattern
     * @param player The player
     * @param uuid The player UUID
     * @param block The placed block
     * @param now Current time
     */
    private void checkScaffoldPattern(Player player, UUID uuid, Block block, long now) {
        Location lastPlace = lastBlockPlace.get(uuid);
        if (lastPlace == null) {
            return;
        }
        
        // Check if blocks are being placed in a line (typical scaffold pattern)
        double distance = block.getLocation().distance(lastPlace);
        
        // Check if blocks are placed too close together too quickly
        if (distance < 2.0 && now - lastBlockPlaceTime.get(uuid) < 200) { // Within 200ms
            // Check if block is placed below player (scaffold pattern)
            Location playerLoc = player.getLocation();
            if (block.getY() < playerLoc.getY() && block.getY() > playerLoc.getY() - 3) {
                // Flag player for violation
                flag(player, "Scaffold pattern detected (Distance: " + String.format("%.2f", distance) + 
                        ", Time: " + (now - lastBlockPlaceTime.get(uuid)) + "ms)");
                LoggerUtil.debug(player.getName() + " failed Scaffold (Pattern: " + 
                        String.format("%.2f", distance) + " blocks, " + 
                        (now - lastBlockPlaceTime.get(uuid)) + "ms)");
            }
        }
    }
    
    /**
     * Checks rotation consistency during scaffolding
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     */
    private void checkRotationConsistency(Player player, UUID uuid, long now) {
        float yaw = player.getLocation().getYaw();
        float pitch = player.getLocation().getPitch();
        
        Float lastYawValue = lastYaw.get(uuid);
        Float lastPitchValue = lastPitch.get(uuid);
        Long lastRotTime = lastRotationTime.get(uuid);
        
        if (lastYawValue != null && lastPitchValue != null && lastRotTime != null) {
            // Calculate rotation change
            double yawDiff = Math.abs(yaw - lastYawValue);
            double pitchDiff = Math.abs(pitch - lastPitchValue);
            
            // Normalize yaw difference
            if (yawDiff > 180) {
                yawDiff = 360 - yawDiff;
            }
            
            double totalRotation = Math.sqrt(yawDiff * yawDiff + pitchDiff * pitchDiff);
            double minRotationChange = getPlugin().getConfig().getDouble("checks.scaffold.min-rotation-change", DEFAULT_MIN_ROTATION_CHANGE);
            
            // Check if rotation is too consistent (possible scaffold bot)
            if (totalRotation < minRotationChange && now - lastRotTime < 500) {
                // Flag player for violation
                flag(player, "Rotation too consistent during scaffolding (Change: " + 
                        String.format("%.2f", totalRotation) + "° < " + minRotationChange + "°)");
                LoggerUtil.debug(player.getName() + " failed Scaffold (Rotation: " + 
                        String.format("%.2f", totalRotation) + "°)");
            }
        }
        
        // Update rotation data
        lastYaw.put(uuid, yaw);
        lastPitch.put(uuid, pitch);
        lastRotationTime.put(uuid, now);
    }
    
    /**
     * Checks movement while scaffolding
     * @param player The player
     * @param event The move event
     */
    private void checkMovementWhileScaffolding(Player player, PlayerMoveEvent event) {
        UUID uuid = player.getUniqueId();
        Long lastPlaceTime = lastBlockPlaceTime.get(uuid);
        
        if (lastPlaceTime == null) {
            return;
        }
        
        long now = System.currentTimeMillis();
        
        // Check if player placed a block recently
        if (now - lastPlaceTime < 1000) { // Within 1 second
            // Calculate movement speed
            Location from = event.getFrom();
            Location to = event.getTo();
            
            if (to == null) return;
            
            double distance = from.distance(to);
            double speed = distance / 0.05; // Assuming 50ms per tick
            
            double maxScaffoldSpeed = getPlugin().getConfig().getDouble("checks.scaffold.max-speed", DEFAULT_MAX_SCAFFOLD_SPEED);
            
            // Check if moving too fast while scaffolding
            if (speed > maxScaffoldSpeed && !player.isSneaking()) {
                // Check if player is placing blocks below them
                Block blockBelow = player.getLocation().subtract(0, 1, 0).getBlock();
                if (blockBelow.getType() == Material.AIR) {
                    // Flag player for violation
                    flag(player, "Moving too fast while scaffolding (Speed: " + 
                            String.format("%.3f", speed) + " > " + maxScaffoldSpeed + ")");
                    LoggerUtil.debug(player.getName() + " failed Scaffold (Speed: " + 
                            String.format("%.3f", speed) + " > " + maxScaffoldSpeed + ")");
                }
            }
        }
    }
    
    @Override
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        blockPlaceCount.remove(uuid);
        lastBlockPlaceTime.remove(uuid);
        lastBlockPlace.remove(uuid);
        lastYaw.remove(uuid);
        lastPitch.remove(uuid);
        lastRotationTime.remove(uuid);
    }
}
