package com.quantum.iceac.detection;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.CheckManager;
import com.quantum.iceac.detection.data.PlayerDataManager;
import com.quantum.iceac.utils.LoggerUtil;

/**
 * Core detection engine for the anti-cheat system
 * Handles the initialization and management of all cheat detection components
 */
public class DetectionEngine {

    private final IceAC plugin;
    private CheckManager checkManager;
    private PlayerDataManager playerDataManager;
    private boolean initialized = false;
    
    public DetectionEngine(IceAC plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Initializes the detection engine
     */
    public void initialize() {
        if (initialized) {
            return;
        }
        
        LoggerUtil.info("Initializing Quantum Detection Engine...");
        
        // Initialize player data manager
        playerDataManager = new PlayerDataManager(plugin);
        playerDataManager.initialize();
        
        // Initialize check manager
        checkManager = new CheckManager(plugin);
        checkManager.registerChecks();
        
        initialized = true;
        LoggerUtil.info("Quantum Detection Engine initialized successfully.");
    }
    
    /**
     * Shuts down the detection engine
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        LoggerUtil.info("Shutting down Quantum Detection Engine...");
        
        // Save player data
        if (playerDataManager != null) {
            playerDataManager.saveAllData();
        }
        
        // Unregister checks
        if (checkManager != null) {
            checkManager.unregisterChecks();
        }
        
        initialized = false;
        LoggerUtil.info("Quantum Detection Engine shut down successfully.");
    }
    
    /**
     * Gets the check manager
     * @return The check manager
     */
    public CheckManager getCheckManager() {
        return checkManager;
    }
    
    /**
     * Gets the player data manager
     * @return The player data manager
     */
    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }
    
    /**
     * Checks if the detection engine is initialized
     * @return True if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}