package com.quantum.iceac.metrics;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Provides a dashboard for viewing analytics data
 */
public class AnalyticsDashboard {

    private final IceAC plugin;
    private final Map<UUID, Long> lastUpdateTime;
    private int taskId;
    
    public AnalyticsDashboard(IceAC plugin) {
        this.plugin = plugin;
        this.lastUpdateTime = new HashMap<>();
        this.taskId = -1;
    }
    
    /**
     * Initializes the analytics dashboard
     */
    public void initialize() {
        // Check if dashboard is enabled
        if (!plugin.getConfig().getBoolean("metrics.dashboard.enabled", true)) {
            LoggerUtil.info("Analytics dashboard is disabled in the configuration.");
            return;
        }
        
        // Schedule dashboard update task
        int updateInterval = plugin.getConfig().getInt("metrics.dashboard.update-interval", 5) * 20;
        taskId = Bukkit.getScheduler().scheduleSyncRepeatingTask(plugin, this::updateDashboard, updateInterval, updateInterval);
        
        LoggerUtil.info("Initialized analytics dashboard.");
    }
    
    /**
     * Updates the dashboard for all players with permission
     */
    private void updateDashboard() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (player.hasPermission("iceac.admin") && isDashboardEnabled(player.getUniqueId())) {
                sendDashboard(player);
            }
        }
    }
    
    /**
     * Sends the dashboard to a player
     * @param player The player to send the dashboard to
     */
    public void sendDashboard(Player player) {
        // Get metrics data
        MetricsCollector metricsCollector = plugin.getMetricsManager().getMetricsCollector();
        int totalViolations = metricsCollector.getTotalViolations();
        int totalPunishments = metricsCollector.getTotalPunishments();
        long uptime = metricsCollector.getUptime();
        
        // Format uptime
        long seconds = uptime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        String uptimeString = days + "d " + (hours % 24) + "h " + (minutes % 60) + "m " + (seconds % 60) + "s";
        
        // Send dashboard header
        player.sendMessage(ChatColor.BLUE + "===== " + ChatColor.AQUA + "IceAC Analytics Dashboard" + ChatColor.BLUE + " =====");
        player.sendMessage(ChatColor.GRAY + "Uptime: " + ChatColor.WHITE + uptimeString);
        player.sendMessage(ChatColor.GRAY + "Total Violations: " + ChatColor.WHITE + totalViolations);
        player.sendMessage(ChatColor.GRAY + "Total Punishments: " + ChatColor.WHITE + totalPunishments);
        
        // Send check type statistics
        player.sendMessage(ChatColor.BLUE + "Check Type Statistics:");
        for (CheckType checkType : CheckType.values()) {
            int typeViolations = 0;
            for (Check check : plugin.getDetectionEngine().getCheckManager().getChecks()) {
                if (check.getType() == checkType) {
                    typeViolations += metricsCollector.getViolations(check.getName());
                }
            }
            
            // Calculate percentage
            double percentage = totalViolations > 0 ? (double) typeViolations / totalViolations * 100 : 0;
            DecimalFormat df = new DecimalFormat("#.##");
            
            player.sendMessage(ChatColor.GRAY + "- " + checkType.getName() + ": " + 
                              ChatColor.WHITE + typeViolations + " " + 
                              ChatColor.GRAY + "(" + df.format(percentage) + "%)");
        }
        
        // Send top checks
        player.sendMessage(ChatColor.BLUE + "Top Checks:");
        Map<String, Integer> checkViolations = new HashMap<>();
        for (Check check : plugin.getDetectionEngine().getCheckManager().getChecks()) {
            checkViolations.put(check.getName(), metricsCollector.getViolations(check.getName()));
        }
        
        // Sort checks by violations
        checkViolations.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(5)
            .forEach(entry -> {
                // Calculate percentage
                double percentage = totalViolations > 0 ? (double) entry.getValue() / totalViolations * 100 : 0;
                DecimalFormat df = new DecimalFormat("#.##");
                
                player.sendMessage(ChatColor.GRAY + "- " + entry.getKey() + ": " + 
                                  ChatColor.WHITE + entry.getValue() + " " + 
                                  ChatColor.GRAY + "(" + df.format(percentage) + "%)");
            });
        
        // Send footer
        player.sendMessage(ChatColor.BLUE + "===================================");
        
        // Update last update time
        lastUpdateTime.put(player.getUniqueId(), System.currentTimeMillis());
    }
    
    /**
     * Checks if the dashboard is enabled for a player
     * @param uuid The UUID of the player
     * @return True if the dashboard is enabled, false otherwise
     */
    public boolean isDashboardEnabled(UUID uuid) {
        return lastUpdateTime.containsKey(uuid);
    }
    
    /**
     * Sets whether the dashboard is enabled for a player
     * @param uuid The UUID of the player
     * @param enabled True if the dashboard is enabled, false otherwise
     */
    public void setDashboardEnabled(UUID uuid, boolean enabled) {
        if (enabled) {
            lastUpdateTime.put(uuid, System.currentTimeMillis());
        } else {
            lastUpdateTime.remove(uuid);
        }
    }
    
    /**
     * Shuts down the analytics dashboard
     */
    public void shutdown() {
        if (taskId != -1) {
            Bukkit.getScheduler().cancelTask(taskId);
            taskId = -1;
        }
    }
}