package com.quantum.iceac.detection.checks.combat;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.util.Vector;

/**
 * Check for KillAura (automated combat hacks)
 */
public class KillAuraCheck extends Check {

    private static final int ROTATION_BUFFER_SIZE = 20;
    private static final double MAX_ANGLE_CHANGE = 30.0;
    private static final double MIN_ANGLE_CHANGE = 2.0;
    private static final int MAX_ATTACKS_PER_SECOND = 20;
    
    public KillAuraCheck(IceAC plugin) {
        super(plugin, "Kill<PERSON><PERSON>", CheckType.COMBAT, "Detects automated combat hacks");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if attacker is a player
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        Entity target = event.getEntity();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Check attack rate
        checkAttackRate(player, playerData);
        
        // Check attack angle
        checkAttackAngle(player, playerData, target);
        
        // Check attack pattern
        checkAttackPattern(player, playerData);
    }
    
    /**
     * Checks the player's attack rate
     * @param player The player
     * @param playerData The player data
     */
    private void checkAttackRate(Player player, PlayerData playerData) {
        long now = System.currentTimeMillis();
        long lastAttackTime = playerData.getLastAttackTime();
        int attackCount = playerData.getAttackCount();
        
        // Reset attack count if more than 1 second has passed
        if (now - lastAttackTime > 1000) {
            playerData.setAttackCount(1);
            playerData.setLastAttackTime(now);
            return;
        }
        
        // Increment attack count
        playerData.setAttackCount(attackCount + 1);
        playerData.setLastAttackTime(now);
        
        // Check if attack count exceeds limit
        if (attackCount > MAX_ATTACKS_PER_SECOND) {
            // Flag player for violation
            flag(player, "Attack rate too high " + attackCount + " attacks/sec");
            LoggerUtil.debug(player.getName() + " failed KillAura (Attack rate: " + attackCount + " attacks/sec)");
        }
    }
    
    /**
     * Checks the player's attack angle
     * @param player The player
     * @param playerData The player data
     * @param target The target entity
     */
    private void checkAttackAngle(Player player, PlayerData playerData, Entity target) {
        // Get player and target locations
        Location playerLoc = player.getLocation();
        Location targetLoc = target.getLocation();
        
        // Calculate angle between player's look vector and vector to target
        Vector lookVec = playerLoc.getDirection();
        Vector toTargetVec = targetLoc.toVector().subtract(playerLoc.toVector()).normalize();
        
        double angle = Math.toDegrees(lookVec.angle(toTargetVec));
        
        // Check if angle is too large
        double maxAngle = getPlugin().getConfig().getDouble("combat.killaura.max-angle", 60.0);
        if (angle > maxAngle) {
            // Flag player for violation
            flag(player, "Attack angle too large " + String.format("%.1f", angle) + "°");
            LoggerUtil.debug(player.getName() + " failed KillAura (Attack angle: " + String.format("%.1f", angle) + "°)");
        }
    }
    
    /**
     * Checks the player's attack pattern
     * @param player The player
     * @param playerData The player data
     */
    private void checkAttackPattern(Player player, PlayerData playerData) {
        // Get player's yaw and pitch
        float yaw = player.getLocation().getYaw();
        float pitch = player.getLocation().getPitch();
        
        // Calculate yaw and pitch changes
        float lastYaw = playerData.getLastYaw();
        float lastPitch = playerData.getLastPitch();
        
        float yawChange = Math.abs(yaw - lastYaw);
        float pitchChange = Math.abs(pitch - lastPitch);
        
        // Normalize yaw change (handle wrap-around)
        if (yawChange > 180) {
            yawChange = 360 - yawChange;
        }
        
        // Check for suspicious rotation patterns
        if (yawChange > MAX_ANGLE_CHANGE && pitchChange < MIN_ANGLE_CHANGE) {
            // Flag player for violation
            flag(player, "Suspicious rotation pattern");
            LoggerUtil.debug(player.getName() + " failed KillAura (Suspicious rotation pattern: yaw=" + 
                    String.format("%.1f", yawChange) + "°, pitch=" + String.format("%.1f", pitchChange) + "°)");
        }
        
        // Check for consistent rotation patterns
        if (playerData.getAttackCount() > 5) {
            float prevYawChange = playerData.getLastYawChange();
            float prevPitchChange = playerData.getLastPitchChange();
            
            // Check if rotation changes are suspiciously consistent
            double yawDiff = Math.abs(yawChange - prevYawChange);
            double pitchDiff = Math.abs(pitchChange - prevPitchChange);
            
            if (yawDiff < 0.1 && pitchDiff < 0.1 && yawChange > 5.0) {
                // Flag player for violation
                flag(player, "Consistent rotation pattern");
                LoggerUtil.debug(player.getName() + " failed KillAura (Consistent rotation pattern: yawDiff=" + 
                        String.format("%.2f", yawDiff) + "°, pitchDiff=" + String.format("%.2f", pitchDiff) + "°)");
            }
        }
        
        // Update player data
        playerData.setLastYaw(yaw);
        playerData.setLastPitch(pitch);
        playerData.setLastYawChange(yawChange);
        playerData.setLastPitchChange(pitchChange);
    }
}