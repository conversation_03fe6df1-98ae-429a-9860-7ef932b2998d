package com.quantum.iceac.detection.checks;

/**
 * Enum for different types of cheat detection checks
 */
public enum CheckType {
    
    COMBAT("Combat", "Checks related to combat, such as KillAura, AutoClicker, etc."),
    MOVEMENT("Movement", "Checks related to movement, such as Flight, Speed, etc."),
    PACKET("Packet", "Checks related to packets, such as invalid packets, packet spam, etc."),
    WORLD("World", "Checks related to world interaction, such as block breaking, item usage, etc."),
    EXPLOIT("Exploit", "Checks related to exploits, such as crash exploits, duplication, etc."),
    OTHER("Other", "Miscellaneous checks that don't fit into other categories.");
    
    private final String name;
    private final String description;
    
    /**
     * Creates a new check type
     * @param name The name of the check type
     * @param description The description of the check type
     */
    CheckType(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    /**
     * Gets the name of the check type
     * @return The name of the check type
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the description of the check type
     * @return The description of the check type
     */
    public String getDescription() {
        return description;
    }
}