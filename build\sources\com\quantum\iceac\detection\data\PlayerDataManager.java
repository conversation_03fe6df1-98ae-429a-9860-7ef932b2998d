package com.quantum.iceac.detection.data;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages player data for the anti-cheat system
 */
public class PlayerDataManager {

    private final IceAC plugin;
    private final Map<UUID, PlayerData> playerDataMap;
    private File dataFolder;
    
    public PlayerDataManager(IceAC plugin) {
        this.plugin = plugin;
        this.playerDataMap = new HashMap<>();
    }
    
    /**
     * Initializes the player data manager
     */
    public void initialize() {
        // Create data folder if it doesn't exist
        dataFolder = new File(plugin.getDataFolder(), "playerdata");
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }
        
        // Create player data for online players
        for (Player player : Bukkit.getOnlinePlayers()) {
            createPlayerData(player);
        }
        
        LoggerUtil.info("Initialized player data manager.");
    }
    
    /**
     * Creates player data for a player
     * @param player The player to create data for
     */
    public void createPlayerData(Player player) {
        UUID uuid = player.getUniqueId();
        
        // Check if player data already exists
        if (playerDataMap.containsKey(uuid)) {
            return;
        }
        
        // Create player data
        PlayerData playerData = new PlayerData(player);
        playerDataMap.put(uuid, playerData);
        
        // Load player data from file if it exists
        File playerFile = new File(dataFolder, uuid.toString() + ".dat");
        if (playerFile.exists()) {
            try {
                playerData.loadFromFile(playerFile);
                LoggerUtil.debug("Loaded player data for " + player.getName() + " from file.");
            } catch (IOException e) {
                LoggerUtil.severe("Failed to load player data for " + player.getName(), e);
            }
        }
    }
    
    /**
     * Gets player data for a player
     * @param player The player to get data for
     * @return The player data, or null if not found
     */
    public PlayerData getPlayerData(Player player) {
        return playerDataMap.get(player.getUniqueId());
    }
    
    /**
     * Gets player data for a player UUID
     * @param uuid The UUID of the player to get data for
     * @return The player data, or null if not found
     */
    public PlayerData getPlayerData(UUID uuid) {
        return playerDataMap.get(uuid);
    }
    
    /**
     * Removes player data for a player
     * @param player The player to remove data for
     */
    public void removePlayerData(Player player) {
        UUID uuid = player.getUniqueId();
        PlayerData playerData = playerDataMap.get(uuid);
        
        if (playerData != null) {
            // Save player data to file
            File playerFile = new File(dataFolder, uuid.toString() + ".dat");
            try {
                playerData.saveToFile(playerFile);
                LoggerUtil.debug("Saved player data for " + player.getName() + " to file.");
            } catch (IOException e) {
                LoggerUtil.severe("Failed to save player data for " + player.getName(), e);
            }
            
            // Remove player data from map
            playerDataMap.remove(uuid);
        }
    }
    
    /**
     * Saves all player data to files
     */
    public void saveAllData() {
        for (Map.Entry<UUID, PlayerData> entry : playerDataMap.entrySet()) {
            UUID uuid = entry.getKey();
            PlayerData playerData = entry.getValue();
            
            // Save player data to file
            File playerFile = new File(dataFolder, uuid.toString() + ".dat");
            try {
                playerData.saveToFile(playerFile);
                LoggerUtil.debug("Saved player data for " + playerData.getPlayerName() + " to file.");
            } catch (IOException e) {
                LoggerUtil.severe("Failed to save player data for " + playerData.getPlayerName(), e);
            }
        }
        
        LoggerUtil.info("Saved all player data to files.");
    }
}