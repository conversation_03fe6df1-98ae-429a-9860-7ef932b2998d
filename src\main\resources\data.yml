# IceAC Data Storage
# This file contains persistent data for the anti-cheat system
# DO NOT EDIT THIS FILE MANUALLY

# Metrics data
metrics:
  # Total violations detected
  total-violations: 0
  
  # Total punishments executed
  total-punishments: 0
  
  # System uptime in milliseconds
  uptime: 0
  
  # Last startup time
  last-startup: 0
  
  # Check-specific violation counts
  check-violations:
    combat:
      killaura: 0
      reach: 0
      autoclicker: 0
      criticals: 0
    movement:
      speed: 0
      fly: 0
      nofall: 0
      jesus: 0
      step: 0
    packet:
      timer: 0
      badpackets: 0
      pingspoof: 0
    world:
      nuker: 0
      fastplace: 0
      xray: 0
    exploit:
      crash: 0
      inventory: 0
      command: 0
    other:
      chatspam: 0
      antiafk: 0
  
  # Check type violation counts
  type-violations:
    combat: 0
    movement: 0
    packet: 0
    world: 0
    exploit: 0
    other: 0

# Player data
players:
  # Format: uuid: {name: "<PERSON><PERSON><PERSON>", violations: {check1: count, check2: count}, last-violation: timestamp, alerts-enabled: boolean}
  # This section will be populated automatically

# Server performance data
performance:
  # Average TPS
  average-tps: 20.0
  
  # Average memory usage (MB)
  average-memory: 0
  
  # Average players online
  average-players: 0
  
  # Performance history (last 24 hours)
  history:
    # This section will be populated automatically

# Neural network data
neural-network:
  # Whether the neural network is trained
  trained: false
  
  # Last training time
  last-training: 0
  
  # Training iterations
  training-iterations: 0
  
  # Accuracy percentage
  accuracy: 0.0
  
  # False positive rate
  false-positive-rate: 0.0
  
  # False negative rate
  false-negative-rate: 0.0

# Version information
version:
  # Current data version
  data-version: 1
  
  # Last plugin version
  last-plugin-version: "1.0.0"