package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Command to display help information
 */
public class HelpCommand implements SubCommand {

    private final IceAC plugin;
    
    public HelpCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "help";
    }
    
    @Override
    public String getDescription() {
        return "Displays help information";
    }
    
    @Override
    public String getUsage() {
        return "/iceac help [page]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        return Arrays.asList("?", "h");
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Get all available commands
        List<SubCommand> availableCommands = new ArrayList<>();
        
        for (SubCommand subCommand : plugin.getCommandManager().getSubCommands()) {
            if (subCommand.getPermission() == null || sender.hasPermission(subCommand.getPermission())) {
                availableCommands.add(subCommand);
            }
        }
        
        // Calculate pagination
        int commandsPerPage = 8;
        int maxPage = (int) Math.ceil((double) availableCommands.size() / commandsPerPage);
        int page = 1;
        
        if (args.length > 0) {
            try {
                page = Integer.parseInt(args[0]);
            } catch (NumberFormatException e) {
                // Invalid page number, use default
            }
        }
        
        // Ensure page is within bounds
        page = Math.max(1, Math.min(maxPage, page));
        
        // Calculate start and end indices
        int startIndex = (page - 1) * commandsPerPage;
        int endIndex = Math.min(startIndex + commandsPerPage, availableCommands.size());
        
        // Send header
        sender.sendMessage(MessageUtil.getMessage("commands.help-header"));
        
        // Send command list
        for (int i = startIndex; i < endIndex; i++) {
            SubCommand subCommand = availableCommands.get(i);
            String message = MessageUtil.getMessage("commands.help-format")
                    .replace("%command%", "iceac " + subCommand.getName())
                    .replace("%description%", subCommand.getDescription());
            sender.sendMessage(message);
        }
        
        // Send footer with pagination info if needed
        if (maxPage > 1) {
            sender.sendMessage(MessageUtil.getMessage("commands.help-footer") + 
                    " &8(&c" + page + "&8/&c" + maxPage + "&8)");
        } else {
            sender.sendMessage(MessageUtil.getMessage("commands.help-footer"));
        }
        
        return true;
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (args.length == 1) {
            // Get all available commands
            List<SubCommand> availableCommands = new ArrayList<>();
            
            for (SubCommand subCommand : plugin.getCommandManager().getSubCommands()) {
                if (subCommand.getPermission() == null || sender.hasPermission(subCommand.getPermission())) {
                    availableCommands.add(subCommand);
                }
            }
            
            // Calculate max page
            int commandsPerPage = 8;
            int maxPage = (int) Math.ceil((double) availableCommands.size() / commandsPerPage);
            
            // Return page numbers
            List<String> pages = new ArrayList<>();
            for (int i = 1; i <= maxPage; i++) {
                pages.add(String.valueOf(i));
            }
            return pages;
        }
        
        return new ArrayList<>();
    }
}