package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;

import java.util.ArrayList;
import java.util.List;

/**
 * Command to reload the plugin
 */
public class ReloadCommand implements SubCommand {

    private final IceAC plugin;
    
    public ReloadCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "reload";
    }
    
    @Override
    public String getDescription() {
        return "Reloads the plugin configuration";
    }
    
    @Override
    public String getUsage() {
        return "/iceac reload";
    }
    
    @Override
    public String getPermission() {
        return "iceac.admin";
    }
    
    @Override
    public List<String> getAliases() {
        List<String> aliases = new ArrayList<>();
        aliases.add("r");
        aliases.add("rl");
        return aliases;
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Send reload start message
        sender.sendMessage(MessageUtil.getMessage("general.reload-start"));
        
        // Reload configuration
        plugin.getConfigManager().reloadAll();
        
        // Reload detection engine
        plugin.getDetectionEngine().reload();
        
        // Reload prevention system
        plugin.getPreventionSystem().reload();
        
        // Reload metrics manager
        plugin.getMetricsManager().reload();
        
        // Send reload complete message
        sender.sendMessage(MessageUtil.getMessage("general.reload-complete"));
        
        return true;
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        return new ArrayList<>();
    }
}