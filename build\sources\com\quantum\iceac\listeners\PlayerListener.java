package com.quantum.iceac.listeners;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * Listener for player-related events
 */
public class PlayerListener implements Listener {

    private final IceAC plugin;
    
    public PlayerListener(IceAC plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Handles player join events
     * @param event The player join event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Create player data
        plugin.getDetectionEngine().getPlayerDataManager().createPlayerData(player);
        
        // Send alerts to staff if player has previous violations
        if (player.hasPermission("iceac.admin") || player.hasPermission("iceac.alerts")) {
            // Enable alerts for staff
            plugin.getMetricsManager().getMetricsCollector().setAlertsEnabled(player.getUniqueId(), true);
        }
        
        // Check if player is exempt from checks
        if (player.hasPermission("iceac.bypass")) {
            PlayerData playerData = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
            if (playerData != null) {
                playerData.setExempt(true);
                LoggerUtil.debug("Player " + player.getName() + " is exempt from checks.");
            }
        }
        
        LoggerUtil.debug("Player " + player.getName() + " joined. Created player data.");
    }
    
    /**
     * Handles player quit events
     * @param event The player quit event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Save and remove player data
        plugin.getDetectionEngine().getPlayerDataManager().removePlayerData(player);
        
        LoggerUtil.debug("Player " + player.getName() + " quit. Removed player data.");
    }
}