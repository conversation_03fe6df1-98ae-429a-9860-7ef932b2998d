# IceAC Whitelist Configuration
# This file contains players who are exempt from anti-cheat checks

# General whitelist settings
general:
  # Whether to enable the whitelist
  enabled: true
  
  # Whether to automatically whitelist operators
  whitelist-ops: true
  
  # Whether to automatically whitelist players with the iceac.bypass permission
  whitelist-permission: true
  
  # Whether to log when a whitelisted player would have been flagged
  log-whitelisted-violations: true

# Whitelisted players
# Format: uuid: {name: "<PERSON><PERSON><PERSON>", reason: "Reason for whitelist", date: "YYYY-MM-DD", checks: ["check1", "check2"]}
# If checks is empty or not specified, the player is exempt from all checks
players:
  # Example entries (replace with your own)
  00000000-0000-0000-0000-000000000000:
    name: "ExampleAdmin"
    reason: "Server administrator"
    date: "2023-01-01"
    checks: []
  
  11111111-1111-1111-1111-111111111111:
    name: "ExampleModerator"
    reason: "Server moderator"
    date: "2023-01-02"
    checks: ["fly", "speed"]

# Whitelist by IP address
# Format: ip: {reason: "Reason for whitelist", date: "YYYY-MM-DD", checks: ["check1", "check2"]}
ips:
  # Example entries (replace with your own)
  "127.0.0.1":
    reason: "Local development"
    date: "2023-01-01"
    checks: []

# Advanced whitelist settings
advanced:
  # Whether to enable temporary whitelisting
  enable-temporary: true
  
  # Default duration for temporary whitelisting (in minutes)
  default-temporary-duration: 60
  
  # Whether to notify staff when a player is whitelisted
  notify-on-whitelist: true
  
  # Whether to notify staff when a temporary whitelist expires
  notify-on-expire: true
  
  # Whether to automatically remove expired temporary whitelists
  auto-remove-expired: true
  
  # Whether to whitelist players in creative mode
  whitelist-creative: false
  
  # Whether to whitelist players in spectator mode
  whitelist-spectator: true
  
  # Whether to whitelist players with specific permissions
  whitelist-permissions:
    - "essentials.god"
    - "essentials.fly"
    - "essentials.speed"