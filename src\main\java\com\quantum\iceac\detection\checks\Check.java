package com.quantum.iceac.detection.checks;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

/**
 * Base class for all cheat detection checks
 */
public abstract class Check {

    protected final IceAC plugin;
    private final String name;
    private final CheckType type;
    private final String description;
    private boolean enabled;
    
    /**
     * Creates a new check
     * @param plugin The plugin instance
     * @param name The name of the check
     * @param type The type of the check
     * @param description The description of the check
     */
    public Check(IceAC plugin, String name, CheckType type, String description) {
        this.plugin = plugin;
        this.name = name;
        this.type = type;
        this.description = description;
        this.enabled = true;
    }
    
    /**
     * Registers the check
     */
    public void register() {
        // Register event listeners
        Bukkit.getPluginManager().registerEvents(this, plugin);
        LoggerUtil.debug("Registered check: " + name);
    }
    
    /**
     * Unregisters the check
     */
    public void unregister() {
        // Unregister event listeners
        LoggerUtil.debug("Unregistered check: " + name);
    }
    
    /**
     * Gets the name of the check
     * @return The name of the check
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the type of the check
     * @return The type of the check
     */
    public CheckType getType() {
        return type;
    }
    
    /**
     * Gets the description of the check
     * @return The description of the check
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if the check is enabled
     * @return True if enabled, false otherwise
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Sets whether the check is enabled
     * @param enabled True if enabled, false otherwise
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    /**
     * Flags a player for a violation
     * @param player The player to flag
     * @param message The violation message
     * @return The number of violations
     */
    protected int flag(Player player, String message) {
        // Check if the player is exempt from checks
        PlayerData playerData = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return 0;
        }
        
        // Add violation
        int violations = playerData.addViolation(name);
        
        // Log violation
        LoggerUtil.debug(player.getName() + " failed " + name + " check: " + message + " (VL: " + violations + ")");
        
        // Send alerts to staff
        sendAlert(player, message, violations);
        
        // Check for punishment
        checkPunishment(player, violations);
        
        return violations;
    }
    
    /**
     * Sends an alert to staff about a violation
     * @param player The player who violated
     * @param message The violation message
     * @param violations The number of violations
     */
    private void sendAlert(Player player, String message, int violations) {
        String alertMessage = plugin.getConfig().getString("prefix", "&b[IceAC] &r") + 
                              "&c" + player.getName() + " &7failed &c" + name + " &7check: &f" + 
                              message + " &7(VL: &c" + violations + "&7)";
        
        // Send alert to console
        LoggerUtil.info(alertMessage.replace("&", "§"));
        
        // Send alert to staff
        for (Player staff : Bukkit.getOnlinePlayers()) {
            if (staff.hasPermission("iceac.alerts")) {
                // Check if alerts are enabled for this staff member
                if (plugin.getMetricsManager().getMetricsCollector().areAlertsEnabled(staff.getUniqueId())) {
                    staff.sendMessage(alertMessage.replace("&", "§"));
                }
            }
        }
    }
    
    /**
     * Checks if a player should be punished for violations
     * @param player The player to check
     * @param violations The number of violations
     */
    private void checkPunishment(Player player, int violations) {
        // Get punishment threshold from configuration
        int threshold = plugin.getConfig().getInt("checks." + name.toLowerCase() + ".punishment-threshold", 10);
        
        // Check if violations exceed threshold
        if (violations >= threshold) {
            // Get punishment type from configuration
            String punishmentType = plugin.getConfig().getString("checks." + name.toLowerCase() + ".punishment-type", "kick");
            
            // Execute punishment
            switch (punishmentType.toLowerCase()) {
                case "kick":
                    // Kick the player
                    String kickMessage = plugin.getConfig().getString("messages.kick", "&cYou have been kicked for cheating.");
                    Bukkit.getScheduler().runTask(plugin, () -> player.kickPlayer(kickMessage.replace("&", "§")));
                    break;
                case "ban":
                    // Ban the player
                    String banMessage = plugin.getConfig().getString("messages.ban", "&cYou have been banned for cheating.");
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        Bukkit.getBanList(org.bukkit.BanList.Type.NAME).addBan(
                            player.getName(),
                            banMessage.replace("&", "§"),
                            null,
                            "IceAC"
                        );
                        player.kickPlayer(banMessage.replace("&", "§"));
                    });
                    break;
                case "command":
                    // Execute a command
                    String command = plugin.getConfig().getString("checks." + name.toLowerCase() + ".punishment-command", "");
                    if (!command.isEmpty()) {
                        String finalCommand = command.replace("%player%", player.getName());
                        Bukkit.getScheduler().runTask(plugin, () -> Bukkit.dispatchCommand(Bukkit.getConsoleSender(), finalCommand));
                    }
                    break;
                default:
                    // No punishment
                    break;
            }
            
            // Reset violations
            PlayerData playerData = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
            if (playerData != null) {
                playerData.resetViolations(name);
            }
        }
    }
}