package com.quantum.iceac.utils;

import com.quantum.iceac.IceAC;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;

/**
 * Utility class for handling messages
 */
public class MessageUtil {

    private static IceAC plugin;
    private static FileConfiguration messagesConfig;
    private static String prefix;
    
    /**
     * Initializes the message utility
     * @param plugin The plugin instance
     */
    public static void initialize(IceAC plugin) {
        MessageUtil.plugin = plugin;
        reload();
    }
    
    /**
     * Reloads the messages configuration
     */
    public static void reload() {
        // Load messages file
        File messagesFile = new File(plugin.getDataFolder(), "messages.yml");
        
        if (!messagesFile.exists()) {
            plugin.saveResource("messages.yml", false);
        }
        
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);
        prefix = colorize(messagesConfig.getString("prefix", "&cIceAC &8| &f"));
        
        LoggerUtil.debug("Loaded messages configuration.");
    }
    
    /**
     * Gets a message from the configuration
     * @param path The message path
     * @return The formatted message
     */
    public static String getMessage(String path) {
        String message = messagesConfig.getString(path);
        
        if (message == null) {
            LoggerUtil.warning("Missing message: " + path);
            return "&cMissing message: &f" + path;
        }
        
        return colorize(message.replace("%prefix%", prefix));
    }
    
    /**
     * Gets a message from the configuration with replacements
     * @param path The message path
     * @param replacements The replacements in format [key1, value1, key2, value2, ...]
     * @return The formatted message
     */
    public static String getMessage(String path, String... replacements) {
        String message = getMessage(path);
        
        if (replacements.length % 2 != 0) {
            LoggerUtil.warning("Invalid replacements for message: " + path);
            return message;
        }
        
        for (int i = 0; i < replacements.length; i += 2) {
            message = message.replace(replacements[i], replacements[i + 1]);
        }
        
        return message;
    }
    
    /**
     * Colorizes a string using color codes
     * @param message The message to colorize
     * @return The colorized message
     */
    public static String colorize(String message) {
        if (message == null) {
            return "";
        }
        
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    /**
     * Gets the prefix
     * @return The prefix
     */
    public static String getPrefix() {
        return prefix;
    }
    
    /**
     * Formats a time in milliseconds to a readable format
     * @param millis The time in milliseconds
     * @return The formatted time
     */
    public static String formatTime(long millis) {
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return days + "d " + (hours % 24) + "h " + (minutes % 60) + "m " + (seconds % 60) + "s";
        } else if (hours > 0) {
            return hours + "h " + (minutes % 60) + "m " + (seconds % 60) + "s";
        } else if (minutes > 0) {
            return minutes + "m " + (seconds % 60) + "s";
        } else {
            return seconds + "s";
        }
    }
}