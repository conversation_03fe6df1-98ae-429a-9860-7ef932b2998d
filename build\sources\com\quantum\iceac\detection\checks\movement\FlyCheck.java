package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.potion.PotionEffectType;

/**
 * Check for Fly (flying without permission)
 */
public class FlyCheck extends Check {

    private static final double DEFAULT_MAX_ASCEND_SPEED = 0.42;
    private static final double DEFAULT_MAX_HOVER_TIME = 1.0; // seconds
    private static final double DEFAULT_MAX_AIR_TIME = 2.5; // seconds
    private static final double GRAVITY_ACCELERATION = 0.08;
    private static final double GRAVITY_DRAG = 0.98;
    
    public FlyCheck(IceAC plugin) {
        super(plugin, "Fly", CheckType.MOVEMENT);
        setDescription("Detects flying without permission");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // Skip if player hasn't moved vertically
        if (from.getY() == to.getY()) {
            return;
        }
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative or spectator mode
        if (player.getGameMode() == GameMode.CREATIVE || player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Skip if player has permission to fly
        if (player.isFlying() || player.getAllowFlight()) {
            return;
        }
        
        // Skip if player is gliding with elytra
        if (player.isGliding()) {
            return;
        }
        
        // Skip if player is in a vehicle
        if (player.isInsideVehicle()) {
            return;
        }
        
        // Skip if player has jump boost effect
        if (player.hasPotionEffect(PotionEffectType.JUMP)) {
            return;
        }
        
        // Skip if player is in water or lava
        Block block = player.getLocation().getBlock();
        if (block.getType() == Material.WATER || block.getType() == Material.LAVA) {
            return;
        }
        
        // Skip if player is on a ladder or vine
        if (block.getType() == Material.LADDER || block.getType() == Material.VINE) {
            return;
        }
        
        // Skip if player is in web
        if (block.getType() == Material.COBWEB) {
            return;
        }
        
        // Check for ascend speed
        double deltaY = to.getY() - from.getY();
        if (deltaY > 0) {
            checkAscendSpeed(player, deltaY);
        }
        
        // Check for hovering
        checkHovering(player, playerData, deltaY);
        
        // Check for air time
        checkAirTime(player, playerData);
    }
    
    /**
     * Checks if a player is ascending too fast
     * @param player The player
     * @param deltaY The vertical movement
     */
    private void checkAscendSpeed(Player player, double deltaY) {
        // Get max ascend speed from rules if available
        double maxAscendSpeed = DEFAULT_MAX_ASCEND_SPEED;
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule ascendSpeedRule = ruleManager.getRule("movement.fly.ascend_speed");
            if (ascendSpeedRule != null) {
                maxAscendSpeed = ascendSpeedRule.getValue();
            }
        }
        
        // Check if player is ascending too fast
        if (deltaY > maxAscendSpeed && !isOnGround(player) && !isNearGround(player)) {
            // Flag player for violation
            flag(player, "Ascending too fast " + String.format("%.2f", deltaY) + " > " + 
                    String.format("%.2f", maxAscendSpeed));
            LoggerUtil.debug(player.getName() + " failed Fly (Ascend: " + 
                    String.format("%.2f", deltaY) + ", Max: " + String.format("%.2f", maxAscendSpeed) + ")");
        }
    }
    
    /**
     * Checks if a player is hovering in the air
     * @param player The player
     * @param playerData The player data
     * @param deltaY The vertical movement
     */
    private void checkHovering(Player player, PlayerData playerData, double deltaY) {
        // Skip if player is on ground
        if (isOnGround(player) || isNearGround(player)) {
            playerData.setHoverTime(0);
            return;
        }
        
        // Check if player is hovering (minimal vertical movement)
        if (Math.abs(deltaY) < 0.03) {
            // Increment hover time
            playerData.setHoverTime(playerData.getHoverTime() + 0.05); // Assuming 50ms per tick
            
            // Get max hover time from rules if available
            double maxHoverTime = DEFAULT_MAX_HOVER_TIME;
            RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
            
            if (ruleManager != null && ruleManager.isInitialized()) {
                Rule hoverTimeRule = ruleManager.getRule("movement.fly.hover_time");
                if (hoverTimeRule != null) {
                    maxHoverTime = hoverTimeRule.getValue();
                }
            }
            
            // Check if player has been hovering too long
            if (playerData.getHoverTime() > maxHoverTime) {
                // Flag player for violation
                flag(player, "Hovering for too long " + String.format("%.2f", playerData.getHoverTime()) + 
                        " > " + String.format("%.2f", maxHoverTime));
                LoggerUtil.debug(player.getName() + " failed Fly (Hover: " + 
                        String.format("%.2f", playerData.getHoverTime()) + ", Max: " + 
                        String.format("%.2f", maxHoverTime) + ")");
                
                // Reset hover time
                playerData.setHoverTime(0);
            }
        } else {
            // Reset hover time if player is not hovering
            playerData.setHoverTime(0);
        }
    }
    
    /**
     * Checks if a player has been in the air for too long
     * @param player The player
     * @param playerData The player data
     */
    private void checkAirTime(Player player, PlayerData playerData) {
        // Skip if player is on ground
        if (isOnGround(player) || isNearGround(player)) {
            playerData.setAirTime(0);
            return;
        }
        
        // Increment air time
        playerData.setAirTime(playerData.getAirTime() + 0.05); // Assuming 50ms per tick
        
        // Get max air time from rules if available
        double maxAirTime = DEFAULT_MAX_AIR_TIME;
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule airTimeRule = ruleManager.getRule("movement.fly.air_time");
            if (airTimeRule != null) {
                maxAirTime = airTimeRule.getValue();
            }
        }
        
        // Check if player has been in the air too long
        if (playerData.getAirTime() > maxAirTime) {
            // Flag player for violation
            flag(player, "In air for too long " + String.format("%.2f", playerData.getAirTime()) + 
                    " > " + String.format("%.2f", maxAirTime));
            LoggerUtil.debug(player.getName() + " failed Fly (Air time: " + 
                    String.format("%.2f", playerData.getAirTime()) + ", Max: " + 
                    String.format("%.2f", maxAirTime) + ")");
            
            // Reset air time
            playerData.setAirTime(0);
        }
    }
    
    /**
     * Checks if a player is on the ground
     * @param player The player
     * @return True if player is on ground, false otherwise
     */
    private boolean isOnGround(Player player) {
        Location location = player.getLocation();
        location.setY(location.getY() - 0.1);
        return location.getBlock().getType().isSolid();
    }
    
    /**
     * Checks if a player is near the ground (within 2 blocks)
     * @param player The player
     * @return True if player is near ground, false otherwise
     */
    private boolean isNearGround(Player player) {
        Location location = player.getLocation();
        for (double y = 0.1; y <= 2.0; y += 0.1) {
            location.setY(player.getLocation().getY() - y);
            if (location.getBlock().getType().isSolid()) {
                return true;
            }
        }
        return false;
    }
}