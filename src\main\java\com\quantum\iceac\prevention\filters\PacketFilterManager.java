package com.quantum.iceac.prevention.filters;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.ProtocolLibrary;
import com.comphenix.protocol.ProtocolManager;
import com.comphenix.protocol.events.ListenerPriority;
import com.comphenix.protocol.events.PacketAdapter;
import com.comphenix.protocol.events.PacketEvent;
import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages packet filtering for the anti-cheat system
 */
public class PacketFilterManager {

    private final IceAC plugin;
    private ProtocolManager protocolManager;
    private final Map<UUID, Long> lastPacketTime;
    private final Map<UUID, Integer> packetCounter;
    private boolean initialized = false;
    
    public PacketFilterManager(IceAC plugin) {
        this.plugin = plugin;
        this.lastPacketTime = new HashMap<>();
        this.packetCounter = new HashMap<>();
    }
    
    /**
     * Initializes the packet filter manager
     */
    public void initialize() {
        // Check if packet filtering is enabled
        if (!plugin.getConfig().getBoolean("prevention.packet-filtering.enabled", true)) {
            LoggerUtil.info("Packet filtering is disabled in the configuration.");
            return;
        }
        
        // Get ProtocolLib manager
        protocolManager = ProtocolLibrary.getProtocolManager();
        
        // Register packet listeners
        registerPacketListeners();
        
        initialized = true;
        LoggerUtil.info("Initialized packet filter manager.");
    }
    
    /**
     * Registers packet listeners
     */
    private void registerPacketListeners() {
        // Register incoming packet listener
        protocolManager.addPacketListener(new PacketAdapter(plugin, ListenerPriority.NORMAL, 
                PacketType.Play.Client.POSITION,
                PacketType.Play.Client.POSITION_LOOK,
                PacketType.Play.Client.LOOK,
                PacketType.Play.Client.FLYING,
                PacketType.Play.Client.ARM_ANIMATION,
                PacketType.Play.Client.USE_ENTITY,
                PacketType.Play.Client.BLOCK_DIG,
                PacketType.Play.Client.BLOCK_PLACE,
                PacketType.Play.Client.ABILITIES,
                PacketType.Play.Client.CUSTOM_PAYLOAD) {
            
            @Override
            public void onPacketReceiving(PacketEvent event) {
                // Process incoming packet
                processIncomingPacket(event);
            }
        });
        
        // Register outgoing packet listener
        protocolManager.addPacketListener(new PacketAdapter(plugin, ListenerPriority.NORMAL,
                PacketType.Play.Server.POSITION,
                PacketType.Play.Server.ENTITY_TELEPORT,
                PacketType.Play.Server.ENTITY_VELOCITY,
                PacketType.Play.Server.EXPLOSION) {
            
            @Override
            public void onPacketSending(PacketEvent event) {
                // Process outgoing packet
                processOutgoingPacket(event);
            }
        });
        
        LoggerUtil.debug("Registered packet listeners.");
    }
    
    /**
     * Processes an incoming packet
     * @param event The packet event
     */
    private void processIncomingPacket(PacketEvent event) {
        Player player = event.getPlayer();
        if (player == null) {
            return;
        }
        
        // Get player data
        PlayerData playerData = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Record packet
        playerData.recordPacket();
        
        // Check for packet spam
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        int maxPacketsPerSecond = plugin.getConfig().getInt("prevention.packet-filtering.max-packets-per-second", 100);
        
        // Initialize or reset counter if needed
        if (!lastPacketTime.containsKey(uuid) || now - lastPacketTime.get(uuid) > 1000) {
            lastPacketTime.put(uuid, now);
            packetCounter.put(uuid, 1);
        } else {
            // Increment counter
            int count = packetCounter.get(uuid) + 1;
            packetCounter.put(uuid, count);
            
            // Check if count exceeds limit
            if (count > maxPacketsPerSecond) {
                // Cancel packet
                event.setCancelled(true);
                LoggerUtil.debug("Cancelled packet from " + player.getName() + " due to packet spam (" + count + " packets/sec).");
                
                // Flag player
                if (count % 10 == 0) { // Only flag every 10 packets to avoid spam
                    plugin.getDetectionEngine().getCheckManager().getCheck("PacketCheck")
                        .flag(player, "Packet spam (" + count + " packets/sec)");
                }
            }
        }
        
        // Process specific packet types
        PacketType packetType = event.getPacketType();
        
        if (packetType == PacketType.Play.Client.POSITION || 
            packetType == PacketType.Play.Client.POSITION_LOOK) {
            // Update player movement data
            double x = event.getPacket().getDoubles().read(0);
            double y = event.getPacket().getDoubles().read(1);
            double z = event.getPacket().getDoubles().read(2);
            float yaw = 0;
            float pitch = 0;
            
            if (packetType == PacketType.Play.Client.POSITION_LOOK) {
                yaw = event.getPacket().getFloat().read(0);
                pitch = event.getPacket().getFloat().read(1);
            } else {
                yaw = player.getLocation().getYaw();
                pitch = player.getLocation().getPitch();
            }
            
            boolean onGround = event.getPacket().getBooleans().read(0);
            
            playerData.updateMovement(x, y, z, yaw, pitch, onGround);
        } else if (packetType == PacketType.Play.Client.USE_ENTITY) {
            // Record attack
            playerData.recordAttack();
        }
    }
    
    /**
     * Processes an outgoing packet
     * @param event The packet event
     */
    private void processOutgoingPacket(PacketEvent event) {
        // Process specific packet types
        // This is where you would implement latency compensation, etc.
    }
    
    /**
     * Shuts down the packet filter manager
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        // Unregister packet listeners
        protocolManager.removePacketListeners(plugin);
        
        initialized = false;
        LoggerUtil.info("Shut down packet filter manager.");
    }
    
    /**
     * Checks if the packet filter manager is initialized
     * @return True if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}