package com.quantum.iceac.detection.neural;

import java.util.Random;

/**
 * Simple neural network implementation for cheat detection
 * Uses a basic feedforward network with one hidden layer
 */
public class NeuralNetwork {

    private final int inputSize;
    private final int hiddenSize;
    private final int outputSize;
    
    // Network weights and biases
    private double[][] weightsInputHidden;
    private double[][] weightsHiddenOutput;
    private double[] hiddenBiases;
    private double[] outputBiases;
    
    // Learning parameters
    private double learningRate = 0.01;
    private final Random random = new Random();
    
    /**
     * Creates a new neural network
     * @param inputSize Number of input neurons
     * @param hiddenSize Number of hidden neurons
     * @param outputSize Number of output neurons
     */
    public NeuralNetwork(int inputSize, int hiddenSize, int outputSize) {
        this.inputSize = inputSize;
        this.hiddenSize = hiddenSize;
        this.outputSize = outputSize;
        
        // Initialize weights and biases
        initializeWeights();
    }
    
    /**
     * Initializes weights and biases with small random values
     */
    private void initializeWeights() {
        // Initialize input to hidden weights
        weightsInputHidden = new double[inputSize][hiddenSize];
        for (int i = 0; i < inputSize; i++) {
            for (int j = 0; j < hiddenSize; j++) {
                weightsInputHidden[i][j] = random.nextGaussian() * 0.1;
            }
        }
        
        // Initialize hidden to output weights
        weightsHiddenOutput = new double[hiddenSize][outputSize];
        for (int i = 0; i < hiddenSize; i++) {
            for (int j = 0; j < outputSize; j++) {
                weightsHiddenOutput[i][j] = random.nextGaussian() * 0.1;
            }
        }
        
        // Initialize biases
        hiddenBiases = new double[hiddenSize];
        outputBiases = new double[outputSize];
        
        for (int i = 0; i < hiddenSize; i++) {
            hiddenBiases[i] = random.nextGaussian() * 0.1;
        }
        
        for (int i = 0; i < outputSize; i++) {
            outputBiases[i] = random.nextGaussian() * 0.1;
        }
    }
    
    /**
     * Randomizes all weights and biases
     */
    public void randomizeWeights() {
        initializeWeights();
    }
    
    /**
     * Predicts output for given input
     * @param input Input values
     * @return Output values
     */
    public double[] predict(double[] input) {
        if (input.length != inputSize) {
            throw new IllegalArgumentException("Input size mismatch. Expected: " + inputSize + ", Got: " + input.length);
        }
        
        // Forward pass through hidden layer
        double[] hidden = new double[hiddenSize];
        for (int j = 0; j < hiddenSize; j++) {
            double sum = hiddenBiases[j];
            for (int i = 0; i < inputSize; i++) {
                sum += input[i] * weightsInputHidden[i][j];
            }
            hidden[j] = sigmoid(sum);
        }
        
        // Forward pass through output layer
        double[] output = new double[outputSize];
        for (int k = 0; k < outputSize; k++) {
            double sum = outputBiases[k];
            for (int j = 0; j < hiddenSize; j++) {
                sum += hidden[j] * weightsHiddenOutput[j][k];
            }
            output[k] = sigmoid(sum);
        }
        
        return output;
    }
    
    /**
     * Trains the network using backpropagation
     * @param input Input values
     * @param target Target output values
     */
    public void train(double[] input, double[] target) {
        if (input.length != inputSize) {
            throw new IllegalArgumentException("Input size mismatch");
        }
        if (target.length != outputSize) {
            throw new IllegalArgumentException("Target size mismatch");
        }
        
        // Forward pass
        double[] hidden = new double[hiddenSize];
        for (int j = 0; j < hiddenSize; j++) {
            double sum = hiddenBiases[j];
            for (int i = 0; i < inputSize; i++) {
                sum += input[i] * weightsInputHidden[i][j];
            }
            hidden[j] = sigmoid(sum);
        }
        
        double[] output = new double[outputSize];
        for (int k = 0; k < outputSize; k++) {
            double sum = outputBiases[k];
            for (int j = 0; j < hiddenSize; j++) {
                sum += hidden[j] * weightsHiddenOutput[j][k];
            }
            output[k] = sigmoid(sum);
        }
        
        // Backward pass - calculate output layer errors
        double[] outputErrors = new double[outputSize];
        for (int k = 0; k < outputSize; k++) {
            outputErrors[k] = (target[k] - output[k]) * sigmoidDerivative(output[k]);
        }
        
        // Calculate hidden layer errors
        double[] hiddenErrors = new double[hiddenSize];
        for (int j = 0; j < hiddenSize; j++) {
            double error = 0.0;
            for (int k = 0; k < outputSize; k++) {
                error += outputErrors[k] * weightsHiddenOutput[j][k];
            }
            hiddenErrors[j] = error * sigmoidDerivative(hidden[j]);
        }
        
        // Update weights and biases
        // Update hidden to output weights
        for (int j = 0; j < hiddenSize; j++) {
            for (int k = 0; k < outputSize; k++) {
                weightsHiddenOutput[j][k] += learningRate * outputErrors[k] * hidden[j];
            }
        }
        
        // Update input to hidden weights
        for (int i = 0; i < inputSize; i++) {
            for (int j = 0; j < hiddenSize; j++) {
                weightsInputHidden[i][j] += learningRate * hiddenErrors[j] * input[i];
            }
        }
        
        // Update output biases
        for (int k = 0; k < outputSize; k++) {
            outputBiases[k] += learningRate * outputErrors[k];
        }
        
        // Update hidden biases
        for (int j = 0; j < hiddenSize; j++) {
            hiddenBiases[j] += learningRate * hiddenErrors[j];
        }
    }
    
    /**
     * Sigmoid activation function
     * @param x Input value
     * @return Sigmoid output
     */
    private double sigmoid(double x) {
        return 1.0 / (1.0 + Math.exp(-x));
    }
    
    /**
     * Derivative of sigmoid function
     * @param sigmoidOutput Output of sigmoid function
     * @return Derivative value
     */
    private double sigmoidDerivative(double sigmoidOutput) {
        return sigmoidOutput * (1.0 - sigmoidOutput);
    }
    
    /**
     * Calculates mean squared error
     * @param predicted Predicted values
     * @param actual Actual values
     * @return Mean squared error
     */
    public double calculateError(double[] predicted, double[] actual) {
        if (predicted.length != actual.length) {
            throw new IllegalArgumentException("Array length mismatch");
        }
        
        double sum = 0.0;
        for (int i = 0; i < predicted.length; i++) {
            double diff = predicted[i] - actual[i];
            sum += diff * diff;
        }
        
        return sum / predicted.length;
    }
    
    /**
     * Sets the learning rate
     * @param learningRate New learning rate
     */
    public void setLearningRate(double learningRate) {
        this.learningRate = learningRate;
    }
    
    /**
     * Gets the learning rate
     * @return Current learning rate
     */
    public double getLearningRate() {
        return learningRate;
    }
    
    /**
     * Gets network information
     * @return Network structure string
     */
    public String getNetworkInfo() {
        return String.format("Neural Network [%d-%d-%d] LR: %.3f", 
                inputSize, hiddenSize, outputSize, learningRate);
    }
    
    /**
     * Trains the network with multiple examples
     * @param inputs Array of input examples
     * @param targets Array of target outputs
     * @param epochs Number of training epochs
     */
    public void trainBatch(double[][] inputs, double[][] targets, int epochs) {
        if (inputs.length != targets.length) {
            throw new IllegalArgumentException("Input and target array lengths must match");
        }
        
        for (int epoch = 0; epoch < epochs; epoch++) {
            for (int i = 0; i < inputs.length; i++) {
                train(inputs[i], targets[i]);
            }
        }
    }
    
    /**
     * Evaluates the network on a test set
     * @param inputs Test inputs
     * @param targets Test targets
     * @return Average error
     */
    public double evaluate(double[][] inputs, double[][] targets) {
        if (inputs.length != targets.length) {
            throw new IllegalArgumentException("Input and target array lengths must match");
        }
        
        double totalError = 0.0;
        for (int i = 0; i < inputs.length; i++) {
            double[] predicted = predict(inputs[i]);
            totalError += calculateError(predicted, targets[i]);
        }
        
        return totalError / inputs.length;
    }
}
