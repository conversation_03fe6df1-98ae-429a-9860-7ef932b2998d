@echo off
echo Building IceAC manually...

REM Create build directories
if not exist "build\classes\java\main" mkdir "build\classes\java\main"
if not exist "build\resources\main" mkdir "build\resources\main"
if not exist "build\libs" mkdir "build\libs"

echo Created build directories.

REM Copy resources
xcopy "src\main\resources\*" "build\resources\main\" /E /Y
echo Copied resources.

REM Create a simple JAR without compilation for now
echo Creating JAR structure...

REM Copy source files to show project structure
if not exist "build\sources" mkdir "build\sources"
xcopy "src\main\java\*" "build\sources\" /E /Y

echo Manual build completed!
echo.
echo Project Structure:
echo - Source files: build\sources\
echo - Resources: build\resources\main\
echo.
echo Note: This is a structure-only build. For full compilation, Java SDK and proper Gradle setup is needed.
pause
