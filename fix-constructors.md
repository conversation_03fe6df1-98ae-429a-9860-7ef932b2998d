# Check Constructor Fixes

## Combat Checks:
1. ✅ AutoClickerCheck - FIXED
2. CriticalsCheck - Need to fix
3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Need to fix  
4. Reach<PERSON><PERSON><PERSON> - Need to fix

## Movement Checks:
1. <PERSON><PERSON><PERSON><PERSON> - Need to fix
2. <PERSON><PERSON><PERSON><PERSON> - Need to fix
3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Need to fix
4. <PERSON><PERSON><PERSON><PERSON> - Need to fix
5. <PERSON><PERSON><PERSON><PERSON> - Need to fix

## Packet Checks:
1. Bad<PERSON>acketsCheck - Need to fix
2. <PERSON><PERSON>poof<PERSON>heck - Need to fix
3. <PERSON><PERSON><PERSON><PERSON><PERSON> - Need to fix

## Pattern:
Change from:
```java
super(plugin, "CheckName", CheckType.TYPE);
setDescription("Description");
```

To:
```java
super(plugin, "CheckName", CheckType.TYPE, "Description");
```
