package com.quantum.iceac.detection.behavioral;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents a behavior event for analysis
 */
public class BehaviorEvent {

    private final BehaviorEventType type;
    private final long timestamp;
    private final Map<String, Object> data;
    
    /**
     * Creates a new behavior event
     * @param type The event type
     * @param timestamp The timestamp
     * @param data Additional event data
     */
    public BehaviorEvent(BehaviorEventType type, long timestamp, Map<String, Object> data) {
        this.type = type;
        this.timestamp = timestamp;
        this.data = data != null ? new HashMap<>(data) : new HashMap<>();
    }
    
    /**
     * Creates a new behavior event with no additional data
     * @param type The event type
     * @param timestamp The timestamp
     */
    public BehaviorEvent(BehaviorEventType type, long timestamp) {
        this(type, timestamp, null);
    }
    
    /**
     * Gets the event type
     * @return The event type
     */
    public BehaviorEventType getType() {
        return type;
    }
    
    /**
     * Gets the timestamp
     * @return The timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }
    
    /**
     * Gets all event data
     * @return The event data map
     */
    public Map<String, Object> getData() {
        return new HashMap<>(data);
    }
    
    /**
     * Gets a specific data value
     * @param key The data key
     * @return The data value, or null if not found
     */
    public Object getData(String key) {
        return data.get(key);
    }
    
    /**
     * Gets a specific data value as a string
     * @param key The data key
     * @return The data value as string, or null if not found
     */
    public String getDataAsString(String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * Gets a specific data value as a double
     * @param key The data key
     * @param defaultValue The default value if not found or not a number
     * @return The data value as double
     */
    public double getDataAsDouble(String key, double defaultValue) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    /**
     * Gets a specific data value as an integer
     * @param key The data key
     * @param defaultValue The default value if not found or not a number
     * @return The data value as integer
     */
    public int getDataAsInt(String key, int defaultValue) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    /**
     * Gets a specific data value as a boolean
     * @param key The data key
     * @param defaultValue The default value if not found
     * @return The data value as boolean
     */
    public boolean getDataAsBoolean(String key, boolean defaultValue) {
        Object value = data.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        try {
            return Boolean.parseBoolean(value.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    /**
     * Sets a data value
     * @param key The data key
     * @param value The data value
     */
    public void setData(String key, Object value) {
        data.put(key, value);
    }
    
    /**
     * Checks if the event has specific data
     * @param key The data key
     * @return True if the data exists
     */
    public boolean hasData(String key) {
        return data.containsKey(key);
    }
    
    /**
     * Gets the age of this event in milliseconds
     * @return The age in milliseconds
     */
    public long getAge() {
        return System.currentTimeMillis() - timestamp;
    }
    
    /**
     * Checks if this event is older than the specified time
     * @param maxAge Maximum age in milliseconds
     * @return True if the event is older than maxAge
     */
    public boolean isOlderThan(long maxAge) {
        return getAge() > maxAge;
    }
    
    @Override
    public String toString() {
        return String.format("BehaviorEvent{type=%s, timestamp=%d, age=%dms, data=%s}", 
                type, timestamp, getAge(), data);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        BehaviorEvent that = (BehaviorEvent) obj;
        return timestamp == that.timestamp && 
               type == that.type && 
               data.equals(that.data);
    }
    
    @Override
    public int hashCode() {
        int result = type.hashCode();
        result = 31 * result + Long.hashCode(timestamp);
        result = 31 * result + data.hashCode();
        return result;
    }
}
