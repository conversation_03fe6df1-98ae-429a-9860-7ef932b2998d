package com.quantum.iceac.config;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages all configuration files for the plugin
 */
public class ConfigManager {

    private final IceAC plugin;
    private FileConfiguration mainConfig;
    private final Map<String, FileConfiguration> configs;
    
    public ConfigManager(IceAC plugin) {
        this.plugin = plugin;
        this.configs = new HashMap<>();
    }
    
    /**
     * Loads all configuration files
     */
    public void loadConfigurations() {
        // Load main config
        plugin.saveDefaultConfig();
        mainConfig = plugin.getConfig();
        
        // Load other configuration files
        loadConfig("checks.yml");
        loadConfig("messages.yml");
        loadConfig("punishments.yml");
        loadConfig("whitelist.yml");
        
        LoggerUtil.info("All configuration files loaded successfully.");
    }
    
    /**
     * Loads a specific configuration file
     * @param fileName The name of the configuration file
     */
    private void loadConfig(String fileName) {
        File configFile = new File(plugin.getDataFolder(), fileName);
        
        if (!configFile.exists()) {
            configFile.getParentFile().mkdirs();
            plugin.saveResource(fileName, false);
        }
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);
        configs.put(fileName, config);
        LoggerUtil.debug("Loaded configuration file: " + fileName);
    }
    
    /**
     * Gets a specific configuration file
     * @param fileName The name of the configuration file
     * @return The configuration file
     */
    public FileConfiguration getConfig(String fileName) {
        return configs.get(fileName);
    }
    
    /**
     * Gets the main configuration file
     * @return The main configuration file
     */
    public FileConfiguration getMainConfig() {
        return mainConfig;
    }
    
    /**
     * Reloads all configuration files
     */
    public void reloadAll() {
        plugin.reloadConfig();
        mainConfig = plugin.getConfig();
        
        for (String fileName : configs.keySet()) {
            loadConfig(fileName);
        }
        
        LoggerUtil.info("All configuration files reloaded successfully.");
    }
    
    /**
     * Saves all configuration files
     */
    public void saveAll() {
        for (Map.Entry<String, FileConfiguration> entry : configs.entrySet()) {
            try {
                File configFile = new File(plugin.getDataFolder(), entry.getKey());
                entry.getValue().save(configFile);
            } catch (IOException e) {
                LoggerUtil.severe("Failed to save configuration file: " + entry.getKey(), e);
            }
        }
        
        plugin.saveConfig();
        LoggerUtil.info("All configuration files saved successfully.");
    }
}