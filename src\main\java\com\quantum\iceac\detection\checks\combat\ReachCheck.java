package com.quantum.iceac.detection.checks.combat;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.util.Vector;

/**
 * Check for Reach (hitting entities from too far away)
 */
public class ReachCheck extends Check {

    private static final double DEFAULT_MAX_REACH = 3.1; // Default max reach distance in blocks
    private static final double DEFAULT_MAX_REACH_CREATIVE = 4.5; // Default max reach distance in creative mode
    
    public ReachCheck(IceAC plugin) {
        super(plugin, "Reach", CheckType.COMBAT, "Detects hitting entities from too far away");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if damager is a player
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        Entity target = event.getEntity();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in spectator mode
        if (player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Calculate reach distance
        double distance = calculateReachDistance(player, target);
        
        // Get max reach from rules if available
        double maxReach = player.getGameMode() == GameMode.CREATIVE ? 
                DEFAULT_MAX_REACH_CREATIVE : DEFAULT_MAX_REACH;
        
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule reachRule = ruleManager.getRule("combat.reach.max_distance");
            if (reachRule != null) {
                maxReach = reachRule.getValue();
                
                // Apply creative mode multiplier
                if (player.getGameMode() == GameMode.CREATIVE) {
                    Rule creativeMultiplierRule = ruleManager.getRule("combat.reach.creative_multiplier");
                    if (creativeMultiplierRule != null) {
                        maxReach *= creativeMultiplierRule.getValue();
                    } else {
                        maxReach = DEFAULT_MAX_REACH_CREATIVE;
                    }
                }
            }
        }
        
        // Apply ping compensation
        int ping = getPing(player);
        if (ping > 100) {
            // Allow more reach for high ping players
            double pingCompensation = Math.min(0.5, (ping - 100) / 1000.0);
            maxReach += pingCompensation;
        }
        
        // Apply TPS compensation
        double tps = getPlugin().getServer().getTPS()[0];
        if (tps < 19.0) {
            // Allow more reach for low TPS
            double tpsCompensation = Math.min(0.5, (20.0 - tps) / 10.0);
            maxReach += tpsCompensation;
        }
        
        // Check if player is reaching too far
        if (distance > maxReach) {
            // Flag player for violation
            flag(player, "Reach too far " + String.format("%.2f", distance) + " > " + 
                    String.format("%.2f", maxReach));
            LoggerUtil.debug(player.getName() + " failed Reach (Distance: " + 
                    String.format("%.2f", distance) + ", Max: " + String.format("%.2f", maxReach) + ")");
        }
    }
    
    /**
     * Calculates the reach distance between a player and a target
     * @param player The player
     * @param target The target entity
     * @return The reach distance in blocks
     */
    private double calculateReachDistance(Player player, Entity target) {
        // Get player eye location
        Location eyeLocation = player.getEyeLocation();
        Vector eyeVector = eyeLocation.toVector();
        
        // Get target location and bounding box
        Location targetLocation = target.getLocation();
        Vector targetVector = targetLocation.toVector();
        
        // Calculate distance between player eyes and target center
        double distance = eyeVector.distance(targetVector);
        
        // Adjust for target hitbox
        double hitboxWidth = target.getWidth() / 2.0;
        double hitboxHeight = target.getHeight() / 2.0;
        
        // Calculate direction vector from player to target
        Vector direction = targetVector.subtract(eyeVector).normalize();
        
        // Calculate closest point on target hitbox to player
        Vector closestPoint = findClosestPointOnHitbox(eyeVector, targetVector, direction, hitboxWidth, hitboxHeight);
        
        // Calculate distance to closest point
        return eyeVector.distance(closestPoint);
    }
    
    /**
     * Finds the closest point on a hitbox to a player
     * @param eyeVector The player's eye location vector
     * @param targetVector The target's location vector
     * @param direction The direction vector from player to target
     * @param hitboxWidth Half the width of the target's hitbox
     * @param hitboxHeight Half the height of the target's hitbox
     * @return The closest point vector
     */
    private Vector findClosestPointOnHitbox(Vector eyeVector, Vector targetVector, Vector direction, 
                                           double hitboxWidth, double hitboxHeight) {
        // Create a simplified hitbox (cuboid)
        double minX = targetVector.getX() - hitboxWidth;
        double maxX = targetVector.getX() + hitboxWidth;
        double minY = targetVector.getY() - hitboxHeight;
        double maxY = targetVector.getY() + hitboxHeight;
        double minZ = targetVector.getZ() - hitboxWidth;
        double maxZ = targetVector.getZ() + hitboxWidth;
        
        // Ray-box intersection
        double tMin = Double.NEGATIVE_INFINITY;
        double tMax = Double.POSITIVE_INFINITY;
        
        // X-axis intersection
        if (Math.abs(direction.getX()) < 1e-6) {
            if (eyeVector.getX() < minX || eyeVector.getX() > maxX) {
                return targetVector; // No intersection
            }
        } else {
            double t1 = (minX - eyeVector.getX()) / direction.getX();
            double t2 = (maxX - eyeVector.getX()) / direction.getX();
            
            if (t1 > t2) {
                double temp = t1;
                t1 = t2;
                t2 = temp;
            }
            
            tMin = Math.max(tMin, t1);
            tMax = Math.min(tMax, t2);
            
            if (tMin > tMax) {
                return targetVector; // No intersection
            }
        }
        
        // Y-axis intersection
        if (Math.abs(direction.getY()) < 1e-6) {
            if (eyeVector.getY() < minY || eyeVector.getY() > maxY) {
                return targetVector; // No intersection
            }
        } else {
            double t1 = (minY - eyeVector.getY()) / direction.getY();
            double t2 = (maxY - eyeVector.getY()) / direction.getY();
            
            if (t1 > t2) {
                double temp = t1;
                t1 = t2;
                t2 = temp;
            }
            
            tMin = Math.max(tMin, t1);
            tMax = Math.min(tMax, t2);
            
            if (tMin > tMax) {
                return targetVector; // No intersection
            }
        }
        
        // Z-axis intersection
        if (Math.abs(direction.getZ()) < 1e-6) {
            if (eyeVector.getZ() < minZ || eyeVector.getZ() > maxZ) {
                return targetVector; // No intersection
            }
        } else {
            double t1 = (minZ - eyeVector.getZ()) / direction.getZ();
            double t2 = (maxZ - eyeVector.getZ()) / direction.getZ();
            
            if (t1 > t2) {
                double temp = t1;
                t1 = t2;
                t2 = temp;
            }
            
            tMin = Math.max(tMin, t1);
            tMax = Math.min(tMax, t2);
            
            if (tMin > tMax) {
                return targetVector; // No intersection
            }
        }
        
        // If we get here, there is an intersection
        if (tMin > 0) {
            // Intersection point is in front of the player
            return eyeVector.clone().add(direction.clone().multiply(tMin));
        } else {
            // Player is inside the hitbox
            return eyeVector.clone();
        }
    }
    
    /**
     * Gets the player's ping
     * @param player The player
     * @return The ping in milliseconds
     */
    private int getPing(Player player) {
        try {
            Object entityPlayer = player.getClass().getMethod("getHandle").invoke(player);
            return (int) entityPlayer.getClass().getField("ping").get(entityPlayer);
        } catch (Exception e) {
            return 0;
        }
    }
}