package com.quantum.iceac.commands.impl;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.commands.SubCommand;
import com.quantum.iceac.utils.MessageUtil;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;

/**
 * Command to toggle violation alerts
 */
public class AlertsCommand implements SubCommand {

    private final IceAC plugin;
    
    public AlertsCommand(IceAC plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public String getName() {
        return "alerts";
    }
    
    @Override
    public String getDescription() {
        return "Toggles violation alerts";
    }
    
    @Override
    public String getUsage() {
        return "/iceac alerts [on|off]";
    }
    
    @Override
    public String getPermission() {
        return "iceac.alerts";
    }
    
    @Override
    public List<String> getAliases() {
        List<String> aliases = new ArrayList<>();
        aliases.add("alert");
        aliases.add("toggle");
        return aliases;
    }
    
    @Override
    public boolean execute(CommandSender sender, String[] args) {
        // Check if sender is a player
        if (!(sender instanceof Player)) {
            sender.sendMessage(MessageUtil.getMessage("general.player-only"));
            return true;
        }
        
        Player player = (Player) sender;
        
        // Get metrics collector
        if (plugin.getMetricsManager() == null || 
            plugin.getMetricsManager().getMetricsCollector() == null) {
            sender.sendMessage(MessageUtil.getPrefix() + "§cMetrics collector is not initialized.");
            return true;
        }
        
        // Toggle alerts
        boolean enabled;
        
        if (args.length > 0) {
            if (args[0].equalsIgnoreCase("on")) {
                enabled = true;
            } else if (args[0].equalsIgnoreCase("off")) {
                enabled = false;
            } else {
                // Invalid argument, toggle current state
                enabled = !plugin.getMetricsManager().getMetricsCollector().hasAlertsEnabled(player);
            }
        } else {
            // No argument, toggle current state
            enabled = !plugin.getMetricsManager().getMetricsCollector().hasAlertsEnabled(player);
        }
        
        // Set alerts state
        plugin.getMetricsManager().getMetricsCollector().setAlertsEnabled(player, enabled);
        
        // Send message
        if (enabled) {
            sender.sendMessage(MessageUtil.getMessage("commands.alerts-enabled"));
        } else {
            sender.sendMessage(MessageUtil.getMessage("commands.alerts-disabled"));
        }
        
        return true;
    }
    
    @Override
    public List<String> tabComplete(CommandSender sender, String[] args) {
        if (args.length == 1) {
            List<String> completions = new ArrayList<>();
            completions.add("on");
            completions.add("off");
            return completions;
        }
        
        return new ArrayList<>();
    }
}