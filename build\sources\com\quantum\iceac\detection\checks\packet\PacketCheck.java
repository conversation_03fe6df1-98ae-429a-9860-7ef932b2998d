package com.quantum.iceac.detection.checks.packet;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.ProtocolLibrary;
import com.comphenix.protocol.events.ListenerPriority;
import com.comphenix.protocol.events.PacketAdapter;
import com.comphenix.protocol.events.PacketEvent;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * General packet analysis check
 */
public class PacketCheck extends Check {

    private static final int DEFAULT_MAX_PACKETS_PER_SECOND = 100;
    private static final int DEFAULT_MAX_MOVEMENT_PACKETS_PER_SECOND = 22;
    private static final int DEFAULT_MAX_KEEP_ALIVE_DELAY = 5000; // 5 seconds
    
    // Packet counters
    private final Map<UUID, Integer> totalPacketCount = new HashMap<>();
    private final Map<UUID, Integer> movementPacketCount = new HashMap<>();
    private final Map<UUID, Long> lastPacketTime = new HashMap<>();
    private final Map<UUID, Long> lastMovementTime = new HashMap<>();
    
    // Keep alive tracking
    private final Map<UUID, Long> keepAliveSentTime = new HashMap<>();
    private final Map<UUID, Integer> keepAliveViolations = new HashMap<>();
    
    public PacketCheck(IceAC plugin) {
        super(plugin, "Packet", CheckType.PACKET, "Analyzes packet patterns and detects anomalies");
    }

    @Override
    public void register() {
        super.register();
        
        // Register packet listeners
        registerPacketListeners();
    }
    
    @Override
    public void unregister() {
        super.unregister();
        
        // Unregister packet listeners
        ProtocolLibrary.getProtocolManager().removePacketListeners(getPlugin());
    }
    
    /**
     * Registers packet listeners using ProtocolLib
     */
    private void registerPacketListeners() {
        // Listen to incoming packets
        ProtocolLibrary.getProtocolManager().addPacketListener(
            new PacketAdapter(getPlugin(), ListenerPriority.MONITOR, 
                PacketType.Play.Client.POSITION,
                PacketType.Play.Client.POSITION_LOOK,
                PacketType.Play.Client.LOOK,
                PacketType.Play.Client.FLYING,
                PacketType.Play.Client.KEEP_ALIVE,
                PacketType.Play.Client.ARM_ANIMATION,
                PacketType.Play.Client.USE_ENTITY) {
                
                @Override
                public void onPacketReceiving(PacketEvent event) {
                    handleIncomingPacket(event);
                }
            }
        );
        
        // Listen to outgoing packets
        ProtocolLibrary.getProtocolManager().addPacketListener(
            new PacketAdapter(getPlugin(), ListenerPriority.MONITOR,
                PacketType.Play.Server.KEEP_ALIVE) {
                
                @Override
                public void onPacketSending(PacketEvent event) {
                    handleOutgoingPacket(event);
                }
            }
        );
    }
    
    /**
     * Handles incoming packets from client
     * @param event The packet event
     */
    private void handleIncomingPacket(PacketEvent event) {
        Player player = event.getPlayer();
        if (player == null) return;
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        long now = System.currentTimeMillis();
        PacketType packetType = event.getPacketType();
        
        // Record packet
        playerData.recordPacket();
        
        // Check packet rate
        checkPacketRate(player, uuid, now);
        
        // Check specific packet types
        if (isMovementPacket(packetType)) {
            checkMovementPacketRate(player, uuid, now);
        } else if (packetType == PacketType.Play.Client.KEEP_ALIVE) {
            handleKeepAliveResponse(player, uuid, now, event);
        }
        
        // Check for packet order violations
        checkPacketOrder(player, packetType, now);
    }
    
    /**
     * Handles outgoing packets to client
     * @param event The packet event
     */
    private void handleOutgoingPacket(PacketEvent event) {
        Player player = event.getPlayer();
        if (player == null) return;
        
        UUID uuid = player.getUniqueId();
        PacketType packetType = event.getPacketType();
        
        if (packetType == PacketType.Play.Server.KEEP_ALIVE) {
            // Record when keep alive was sent
            keepAliveSentTime.put(uuid, System.currentTimeMillis());
        }
    }
    
    /**
     * Checks if packet type is a movement packet
     * @param packetType The packet type
     * @return True if movement packet
     */
    private boolean isMovementPacket(PacketType packetType) {
        return packetType == PacketType.Play.Client.POSITION ||
               packetType == PacketType.Play.Client.POSITION_LOOK ||
               packetType == PacketType.Play.Client.LOOK ||
               packetType == PacketType.Play.Client.FLYING;
    }
    
    /**
     * Checks overall packet rate
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     */
    private void checkPacketRate(Player player, UUID uuid, long now) {
        // Initialize or reset counter if needed
        if (!lastPacketTime.containsKey(uuid) || now - lastPacketTime.get(uuid) > 1000) {
            lastPacketTime.put(uuid, now);
            totalPacketCount.put(uuid, 1);
            return;
        }
        
        // Increment counter
        int count = totalPacketCount.get(uuid) + 1;
        totalPacketCount.put(uuid, count);
        
        // Check if within the same second
        if (now - lastPacketTime.get(uuid) < 1000) {
            int maxPackets = getPlugin().getConfig().getInt("checks.packet.max-packets-per-second", DEFAULT_MAX_PACKETS_PER_SECOND);
            
            if (count > maxPackets) {
                // Flag player for violation
                flag(player, "Too many packets per second (" + count + " > " + maxPackets + ")");
                LoggerUtil.debug(player.getName() + " failed Packet (Rate: " + count + " > " + maxPackets + ")");
            }
        }
    }
    
    /**
     * Checks movement packet rate
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     */
    private void checkMovementPacketRate(Player player, UUID uuid, long now) {
        // Initialize or reset counter if needed
        if (!lastMovementTime.containsKey(uuid) || now - lastMovementTime.get(uuid) > 1000) {
            lastMovementTime.put(uuid, now);
            movementPacketCount.put(uuid, 1);
            return;
        }
        
        // Increment counter
        int count = movementPacketCount.get(uuid) + 1;
        movementPacketCount.put(uuid, count);
        
        // Check if within the same second
        if (now - lastMovementTime.get(uuid) < 1000) {
            int maxMovementPackets = getPlugin().getConfig().getInt("checks.packet.max-movement-packets-per-second", DEFAULT_MAX_MOVEMENT_PACKETS_PER_SECOND);
            
            if (count > maxMovementPackets) {
                // Flag player for violation
                flag(player, "Too many movement packets per second (" + count + " > " + maxMovementPackets + ")");
                LoggerUtil.debug(player.getName() + " failed Packet (Movement rate: " + count + " > " + maxMovementPackets + ")");
            }
        }
    }
    
    /**
     * Handles keep alive response
     * @param player The player
     * @param uuid The player UUID
     * @param now Current time
     * @param event The packet event
     */
    private void handleKeepAliveResponse(Player player, UUID uuid, long now, PacketEvent event) {
        Long sentTime = keepAliveSentTime.get(uuid);
        if (sentTime == null) {
            return;
        }
        
        long responseTime = now - sentTime;
        int maxDelay = getPlugin().getConfig().getInt("checks.packet.max-keep-alive-delay", DEFAULT_MAX_KEEP_ALIVE_DELAY);
        
        if (responseTime > maxDelay) {
            int violations = keepAliveViolations.getOrDefault(uuid, 0) + 1;
            keepAliveViolations.put(uuid, violations);
            
            if (violations > 3) {
                // Flag player for violation
                flag(player, "Keep alive response too slow (" + responseTime + "ms > " + maxDelay + "ms)");
                LoggerUtil.debug(player.getName() + " failed Packet (Keep alive delay: " + responseTime + "ms)");
                
                // Reset violations
                keepAliveViolations.put(uuid, 0);
            }
        } else {
            // Reset violations on good response
            keepAliveViolations.put(uuid, 0);
        }
        
        // Remove sent time
        keepAliveSentTime.remove(uuid);
    }
    
    /**
     * Checks packet order for violations
     * @param player The player
     * @param packetType The packet type
     * @param now Current time
     */
    private void checkPacketOrder(Player player, PacketType packetType, long now) {
        // This could be expanded to check for packet order violations
        // For example, checking if movement packets are sent in the correct order
        
        // Basic implementation - could be enhanced
        if (packetType == PacketType.Play.Client.ARM_ANIMATION) {
            // Check if arm animation is sent too frequently
            UUID uuid = player.getUniqueId();
            Long lastAnim = lastPacketTime.get(uuid);
            
            if (lastAnim != null && now - lastAnim < 50) { // Less than 50ms
                // Flag player for violation
                flag(player, "Arm animation packets sent too frequently");
                LoggerUtil.debug(player.getName() + " failed Packet (Arm animation spam)");
            }
        }
    }
    
    @Override
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        totalPacketCount.remove(uuid);
        movementPacketCount.remove(uuid);
        lastPacketTime.remove(uuid);
        lastMovementTime.remove(uuid);
        keepAliveSentTime.remove(uuid);
        keepAliveViolations.remove(uuid);
    }
}
