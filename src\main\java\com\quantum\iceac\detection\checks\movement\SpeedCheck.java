package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

/**
 * Check for Speed (moving faster than allowed)
 */
public class SpeedCheck extends Check {

    private static final double DEFAULT_HORIZONTAL_SPEED = 0.3;
    private static final double DEFAULT_SPRINT_MULTIPLIER = 1.3;
    private static final double DEFAULT_SNEAK_MULTIPLIER = 0.3;
    private static final double DEFAULT_SWIM_MULTIPLIER = 0.5;
    private static final double DEFAULT_WATER_MULTIPLIER = 0.8;
    private static final double DEFAULT_ICE_MULTIPLIER = 1.5;
    private static final double DEFAULT_SOUL_SAND_MULTIPLIER = 0.4;
    
    public SpeedCheck(IceAC plugin) {
        super(plugin, "Speed", CheckType.MOVEMENT);
        setDescription("Detects moving faster than allowed");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // Skip if player hasn't moved horizontally
        if (from.getX() == to.getX() && from.getZ() == to.getZ()) {
            return;
        }
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative or spectator mode
        if (player.getGameMode() == GameMode.CREATIVE || player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Skip if player is flying or gliding
        if (player.isFlying() || player.isGliding()) {
            return;
        }
        
        // Skip if player is in a vehicle
        if (player.isInsideVehicle()) {
            return;
        }
        
        // Calculate horizontal distance
        double deltaX = to.getX() - from.getX();
        double deltaZ = to.getZ() - from.getZ();
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        // Get maximum allowed speed
        double maxSpeed = getMaxAllowedSpeed(player, from, to);
        
        // Check if player is moving too fast
        if (distance > maxSpeed) {
            // Flag player for violation
            flag(player, "Moving too fast " + String.format("%.2f", distance) + " > " + 
                    String.format("%.2f", maxSpeed));
            LoggerUtil.debug(player.getName() + " failed Speed (Distance: " + 
                    String.format("%.2f", distance) + ", Max: " + String.format("%.2f", maxSpeed) + ")");
        }
    }
    
    /**
     * Gets the maximum allowed speed for a player
     * @param player The player
     * @param from The from location
     * @param to The to location
     * @return The maximum allowed speed
     */
    private double getMaxAllowedSpeed(Player player, Location from, Location to) {
        // Get base speed from rules if available
        double baseSpeed = DEFAULT_HORIZONTAL_SPEED;
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule horizontalSpeedRule = ruleManager.getRule("movement.speed.horizontal");
            if (horizontalSpeedRule != null) {
                baseSpeed = horizontalSpeedRule.getValue();
            }
        }
        
        // Apply multipliers based on player state
        double multiplier = 1.0;
        
        // Check if player is sprinting
        if (player.isSprinting()) {
            double sprintMultiplier = DEFAULT_SPRINT_MULTIPLIER;
            
            if (ruleManager != null && ruleManager.isInitialized()) {
                Rule sprintRule = ruleManager.getRule("movement.speed.sprint");
                if (sprintRule != null) {
                    sprintMultiplier = 1.0 + sprintRule.getValue();
                }
            }
            
            multiplier *= sprintMultiplier;
        }
        
        // Check if player is sneaking
        if (player.isSneaking()) {
            double sneakMultiplier = DEFAULT_SNEAK_MULTIPLIER;
            
            if (ruleManager != null && ruleManager.isInitialized()) {
                Rule sneakRule = ruleManager.getRule("movement.speed.sneak");
                if (sneakRule != null) {
                    sneakMultiplier = sneakRule.getValue();
                }
            }
            
            multiplier *= sneakMultiplier;
        }
        
        // Check if player is swimming
        if (player.isSwimming()) {
            double swimMultiplier = DEFAULT_SWIM_MULTIPLIER;
            
            if (ruleManager != null && ruleManager.isInitialized()) {
                Rule swimRule = ruleManager.getRule("movement.speed.swim");
                if (swimRule != null) {
                    swimMultiplier = swimRule.getValue();
                }
            }
            
            multiplier *= swimMultiplier;
        }
        
        // Check if player is in water
        if (player.getLocation().getBlock().getType() == Material.WATER) {
            multiplier *= DEFAULT_WATER_MULTIPLIER;
        }
        
        // Check if player is on ice
        if (player.getLocation().subtract(0, 0.1, 0).getBlock().getType().toString().contains("ICE")) {
            multiplier *= DEFAULT_ICE_MULTIPLIER;
        }
        
        // Check if player is on soul sand
        if (player.getLocation().subtract(0, 0.1, 0).getBlock().getType() == Material.SOUL_SAND) {
            multiplier *= DEFAULT_SOUL_SAND_MULTIPLIER;
        }
        
        // Apply speed potion effect
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (effect.getType().equals(PotionEffectType.SPEED)) {
                int level = effect.getAmplifier() + 1;
                multiplier *= 1.0 + (level * 0.2);
            }
        }
        
        // Apply slowness potion effect
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (effect.getType().equals(PotionEffectType.SLOW)) {
                int level = effect.getAmplifier() + 1;
                multiplier *= 1.0 - (level * 0.15);
            }
        }
        
        // Apply server lag compensation
        double tps = getPlugin().getServer().getTPS()[0];
        if (tps < 20.0) {
            double lagCompensation = Math.max(0.8, tps / 20.0);
            multiplier /= lagCompensation;
        }
        
        // Apply ping compensation
        int ping = getPing(player);
        if (ping > 100) {
            double pingCompensation = Math.min(1.5, 1.0 + (ping - 100) / 1000.0);
            multiplier *= pingCompensation;
        }
        
        return baseSpeed * multiplier;
    }
    
    /**
     * Gets the player's ping
     * @param player The player
     * @return The ping in milliseconds
     */
    private int getPing(Player player) {
        try {
            Object entityPlayer = player.getClass().getMethod("getHandle").invoke(player);
            return (int) entityPlayer.getClass().getField("ping").get(entityPlayer);
        } catch (Exception e) {
            return 0;
        }
    }
}