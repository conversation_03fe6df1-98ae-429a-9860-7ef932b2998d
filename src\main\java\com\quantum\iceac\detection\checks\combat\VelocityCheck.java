package com.quantum.iceac.detection.checks.combat;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerVelocityEvent;
import org.bukkit.util.Vector;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for Velocity/Knockback modifications
 */
public class VelocityCheck extends Check {

    private static final double DEFAULT_MIN_VELOCITY_RATIO = 0.7;
    private static final double DEFAULT_MAX_VELOCITY_RATIO = 1.3;
    private static final long DEFAULT_VELOCITY_TIMEOUT = 2000; // 2 seconds
    private static final double DEFAULT_MIN_VELOCITY_MAGNITUDE = 0.1;
    
    // Track velocity data
    private final Map<UUID, Vector> expectedVelocity = new HashMap<>();
    private final Map<UUID, Long> velocityTime = new HashMap<>();
    private final Map<UUID, Vector> actualMovement = new HashMap<>();
    private final Map<UUID, Integer> velocityViolations = new HashMap<>();
    
    public VelocityCheck(IceAC plugin) {
        super(plugin, "Velocity", CheckType.COMBAT, "Detects velocity/knockback modifications");
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerVelocity(PlayerVelocityEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        Vector velocity = event.getVelocity();
        
        // Only track significant velocity changes
        if (velocity.length() > DEFAULT_MIN_VELOCITY_MAGNITUDE) {
            expectedVelocity.put(uuid, velocity.clone());
            velocityTime.put(uuid, System.currentTimeMillis());
            
            LoggerUtil.debug(player.getName() + " received velocity: " + 
                    String.format("%.3f", velocity.length()));
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        
        // Check if we're tracking velocity for this player
        if (!expectedVelocity.containsKey(uuid) || !velocityTime.containsKey(uuid)) {
            return;
        }
        
        long now = System.currentTimeMillis();
        long velocityAge = now - velocityTime.get(uuid);
        
        // Check if velocity has expired
        long velocityTimeout = getPlugin().getConfig().getLong("checks.velocity.timeout", DEFAULT_VELOCITY_TIMEOUT);
        if (velocityAge > velocityTimeout) {
            // Clean up expired velocity data
            expectedVelocity.remove(uuid);
            velocityTime.remove(uuid);
            actualMovement.remove(uuid);
            return;
        }
        
        // Calculate actual movement
        Vector movement = event.getTo().toVector().subtract(event.getFrom().toVector());
        actualMovement.put(uuid, movement);
        
        // Check velocity compliance after a short delay
        if (velocityAge > 100 && velocityAge < 1000) { // Between 100ms and 1s
            checkVelocityCompliance(player, uuid);
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Record that player was damaged (for context)
        UUID uuid = player.getUniqueId();
        velocityTime.put(uuid, System.currentTimeMillis());
    }
    
    /**
     * Checks if player is complying with expected velocity
     * @param player The player
     * @param uuid The player UUID
     */
    private void checkVelocityCompliance(Player player, UUID uuid) {
        Vector expected = expectedVelocity.get(uuid);
        Vector actual = actualMovement.get(uuid);
        
        if (expected == null || actual == null) {
            return;
        }
        
        // Calculate velocity ratios
        double expectedMagnitude = expected.length();
        double actualMagnitude = actual.length();
        
        if (expectedMagnitude < DEFAULT_MIN_VELOCITY_MAGNITUDE) {
            return; // Ignore very small velocities
        }
        
        double ratio = actualMagnitude / expectedMagnitude;
        
        // Get thresholds from config
        double minRatio = getPlugin().getConfig().getDouble("checks.velocity.min-ratio", DEFAULT_MIN_VELOCITY_RATIO);
        double maxRatio = getPlugin().getConfig().getDouble("checks.velocity.max-ratio", DEFAULT_MAX_VELOCITY_RATIO);
        
        // Check if velocity is too low (anti-knockback)
        if (ratio < minRatio) {
            int violations = velocityViolations.getOrDefault(uuid, 0) + 1;
            velocityViolations.put(uuid, violations);
            
            // Flag player for violation
            flag(player, "Velocity too low (Ratio: " + String.format("%.3f", ratio) + 
                    " < " + minRatio + ", Expected: " + String.format("%.3f", expectedMagnitude) + 
                    ", Actual: " + String.format("%.3f", actualMagnitude) + ")");
            LoggerUtil.debug(player.getName() + " failed Velocity (Low: " + 
                    String.format("%.3f", ratio) + ", Violations: " + violations + ")");
        }
        // Check if velocity is too high (velocity modifier)
        else if (ratio > maxRatio) {
            int violations = velocityViolations.getOrDefault(uuid, 0) + 1;
            velocityViolations.put(uuid, violations);
            
            // Flag player for violation
            flag(player, "Velocity too high (Ratio: " + String.format("%.3f", ratio) + 
                    " > " + maxRatio + ", Expected: " + String.format("%.3f", expectedMagnitude) + 
                    ", Actual: " + String.format("%.3f", actualMagnitude) + ")");
            LoggerUtil.debug(player.getName() + " failed Velocity (High: " + 
                    String.format("%.3f", ratio) + ", Violations: " + violations + ")");
        } else {
            // Reset violations on good behavior
            velocityViolations.put(uuid, Math.max(0, velocityViolations.getOrDefault(uuid, 0) - 1));
        }
        
        // Check directional compliance
        checkDirectionalCompliance(player, uuid, expected, actual);
        
        // Clean up after check
        expectedVelocity.remove(uuid);
        velocityTime.remove(uuid);
        actualMovement.remove(uuid);
    }
    
    /**
     * Checks if player is moving in the expected direction
     * @param player The player
     * @param uuid The player UUID
     * @param expected The expected velocity
     * @param actual The actual movement
     */
    private void checkDirectionalCompliance(Player player, UUID uuid, Vector expected, Vector actual) {
        // Normalize vectors for direction comparison
        Vector expectedDir = expected.clone().normalize();
        Vector actualDir = actual.clone().normalize();
        
        // Calculate angle between vectors
        double dotProduct = expectedDir.dot(actualDir);
        double angle = Math.acos(Math.max(-1.0, Math.min(1.0, dotProduct))) * 180.0 / Math.PI;
        
        // Get threshold from config
        double maxAngle = getPlugin().getConfig().getDouble("checks.velocity.max-angle", 45.0);
        
        // Check if direction is too different
        if (angle > maxAngle && expected.length() > 0.2) { // Only check for significant velocities
            // Flag player for violation
            flag(player, "Velocity direction wrong (Angle: " + String.format("%.1f", angle) + 
                    "° > " + maxAngle + "°)");
            LoggerUtil.debug(player.getName() + " failed Velocity (Direction: " + 
                    String.format("%.1f", angle) + "°)");
        }
    }
    
    /**
     * Checks for velocity patterns that indicate cheating
     * @param player The player
     * @param uuid The player UUID
     */
    private void checkVelocityPatterns(Player player, UUID uuid) {
        int violations = velocityViolations.getOrDefault(uuid, 0);
        
        // Check for consistent velocity violations
        if (violations > 5) {
            // Flag player for violation
            flag(player, "Consistent velocity violations (Count: " + violations + ")");
            LoggerUtil.debug(player.getName() + " failed Velocity (Pattern: " + violations + " violations)");
            
            // Reset counter
            velocityViolations.put(uuid, 0);
        }
    }
    
    @Override
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        expectedVelocity.remove(uuid);
        velocityTime.remove(uuid);
        actualMovement.remove(uuid);
        velocityViolations.remove(uuid);
    }
}
