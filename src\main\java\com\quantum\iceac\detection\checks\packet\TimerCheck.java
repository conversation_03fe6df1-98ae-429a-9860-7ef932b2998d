package com.quantum.iceac.detection.checks.packet;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for Timer (manipulating game speed)
 */
public class TimerCheck extends Check {

    private static final double DEFAULT_MAX_PACKETS_PER_SECOND = 22.0;
    private static final double DEFAULT_MIN_PACKETS_PER_SECOND = 18.0;
    private static final long SAMPLE_TIME = 5000; // 5 seconds
    
    private final Map<UUID, Long> lastCheckTime = new HashMap<>();
    private final Map<UUID, Integer> packetCount = new HashMap<>();
    
    public TimerCheck(IceAC plugin) {
        super(plugin, "Timer", CheckType.PACKET);
        setDescription("Detects manipulating game speed");
    }
    
    /**
     * Called when a player sends a movement packet
     * @param player The player
     */
    public void onMovementPacket(Player player) {
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        // Initialize if first packet
        if (!lastCheckTime.containsKey(uuid)) {
            lastCheckTime.put(uuid, currentTime);
            packetCount.put(uuid, 1);
            return;
        }
        
        // Increment packet count
        packetCount.put(uuid, packetCount.get(uuid) + 1);
        
        // Check if sample time has elapsed
        long timeDiff = currentTime - lastCheckTime.get(uuid);
        if (timeDiff >= SAMPLE_TIME) {
            // Calculate packets per second
            double packetsPerSecond = (packetCount.get(uuid) / (timeDiff / 1000.0));
            
            // Get max packets per second from rules if available
            double maxPacketsPerSecond = DEFAULT_MAX_PACKETS_PER_SECOND;
            double minPacketsPerSecond = DEFAULT_MIN_PACKETS_PER_SECOND;
            RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
            
            if (ruleManager != null && ruleManager.isInitialized()) {
                Rule maxPacketsRule = ruleManager.getRule("packet.timer.max_packets");
                if (maxPacketsRule != null) {
                    maxPacketsPerSecond = maxPacketsRule.getValue();
                }
                
                Rule minPacketsRule = ruleManager.getRule("packet.timer.min_packets");
                if (minPacketsRule != null) {
                    minPacketsPerSecond = minPacketsRule.getValue();
                }
            }
            
            // Apply ping compensation
            int ping = getPing(player);
            if (ping > 100) {
                // Allow more packets for high ping players
                double pingCompensation = Math.min(1.5, 1.0 + (ping - 100) / 1000.0);
                maxPacketsPerSecond *= pingCompensation;
            }
            
            // Apply TPS compensation
            double tps = getPlugin().getServer().getTPS()[0];
            if (tps < 19.0) {
                // Allow fewer packets for low TPS
                double tpsCompensation = Math.max(0.8, tps / 20.0);
                minPacketsPerSecond *= tpsCompensation;
            }
            
            // Check if player is sending too many packets (Timer+)
            if (packetsPerSecond > maxPacketsPerSecond) {
                // Flag player for violation
                flag(player, "Timer+ (" + String.format("%.2f", packetsPerSecond) + " > " + 
                        String.format("%.2f", maxPacketsPerSecond) + " packets/sec)");
                LoggerUtil.debug(player.getName() + " failed Timer+ (" + 
                        String.format("%.2f", packetsPerSecond) + " > " + 
                        String.format("%.2f", maxPacketsPerSecond) + " packets/sec)");
            }
            
            // Check if player is sending too few packets (Timer-)
            else if (packetsPerSecond < minPacketsPerSecond) {
                // Flag player for violation
                flag(player, "Timer- (" + String.format("%.2f", packetsPerSecond) + " < " + 
                        String.format("%.2f", minPacketsPerSecond) + " packets/sec)");
                LoggerUtil.debug(player.getName() + " failed Timer- (" + 
                        String.format("%.2f", packetsPerSecond) + " < " + 
                        String.format("%.2f", minPacketsPerSecond) + " packets/sec)");
            }
            
            // Reset counters
            lastCheckTime.put(uuid, currentTime);
            packetCount.put(uuid, 0);
        }
    }
    
    /**
     * Called when a player quits
     * @param player The player
     */
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        lastCheckTime.remove(uuid);
        packetCount.remove(uuid);
    }
    
    /**
     * Gets the player's ping
     * @param player The player
     * @return The ping in milliseconds
     */
    private int getPing(Player player) {
        try {
            Object entityPlayer = player.getClass().getMethod("getHandle").invoke(player);
            return (int) entityPlayer.getClass().getField("ping").get(entityPlayer);
        } catch (Exception e) {
            return 0;
        }
    }
}