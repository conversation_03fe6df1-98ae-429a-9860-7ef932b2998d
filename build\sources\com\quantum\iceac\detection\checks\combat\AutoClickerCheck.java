package com.quantum.iceac.detection.checks.combat;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.*;

/**
 * Check for AutoClicker (clicking too fast or with suspicious patterns)
 */
public class AutoClickerCheck extends Check {

    private static final int DEFAULT_MAX_CPS = 20; // Default max clicks per second
    private static final int DEFAULT_MIN_CPS = 2; // Default min clicks per second
    private static final int SAMPLE_SIZE = 20; // Number of clicks to analyze
    private static final long CLICK_TIMEOUT = 2000; // Time in ms to reset click counter
    
    private final Map<UUID, List<Long>> clickTimestamps = new HashMap<>();
    private final Map<UUID, Long> lastClickTime = new HashMap<>();
    
    public AutoClickerCheck(IceAC plugin) {
        super(plugin, "AutoClicker", CheckType.COMBAT);
        setDescription("Detects clicking too fast or with suspicious patterns");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(PlayerInteractEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        // Check if action is left or right click
        if (event.getAction() != Action.LEFT_CLICK_AIR && 
                event.getAction() != Action.LEFT_CLICK_BLOCK && 
                event.getAction() != Action.RIGHT_CLICK_AIR && 
                event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Process click
        processClick(player, event.getAction() == Action.LEFT_CLICK_AIR || 
                event.getAction() == Action.LEFT_CLICK_BLOCK);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();
        
        // Clean up maps
        clickTimestamps.remove(uuid);
        lastClickTime.remove(uuid);
    }
    
    /**
     * Processes a click event
     * @param player The player
     * @param isLeftClick Whether the click is a left click
     */
    private void processClick(Player player, boolean isLeftClick) {
        UUID uuid = player.getUniqueId();
        long currentTime = System.currentTimeMillis();
        
        // Initialize if first click
        if (!clickTimestamps.containsKey(uuid)) {
            clickTimestamps.put(uuid, new ArrayList<>());
            lastClickTime.put(uuid, currentTime);
        }
        
        // Check if click timeout has elapsed
        if (currentTime - lastClickTime.get(uuid) > CLICK_TIMEOUT) {
            // Reset click timestamps
            clickTimestamps.get(uuid).clear();
        }
        
        // Add current timestamp
        clickTimestamps.get(uuid).add(currentTime);
        lastClickTime.put(uuid, currentTime);
        
        // Check if we have enough samples
        if (clickTimestamps.get(uuid).size() >= SAMPLE_SIZE) {
            // Analyze clicks
            analyzeClicks(player, clickTimestamps.get(uuid), isLeftClick);
            
            // Remove oldest click
            clickTimestamps.get(uuid).remove(0);
        }
    }
    
    /**
     * Analyzes click patterns
     * @param player The player
     * @param timestamps The click timestamps
     * @param isLeftClick Whether the clicks are left clicks
     */
    private void analyzeClicks(Player player, List<Long> timestamps, boolean isLeftClick) {
        // Calculate clicks per second
        long timeSpan = timestamps.get(timestamps.size() - 1) - timestamps.get(0);
        double seconds = timeSpan / 1000.0;
        double cps = timestamps.size() / seconds;
        
        // Get max CPS from rules if available
        double maxCPS = DEFAULT_MAX_CPS;
        double minCPS = DEFAULT_MIN_CPS;
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule maxCPSRule = isLeftClick ? 
                    ruleManager.getRule("combat.autoclicker.max_left_cps") : 
                    ruleManager.getRule("combat.autoclicker.max_right_cps");
            
            if (maxCPSRule != null) {
                maxCPS = maxCPSRule.getValue();
            }
            
            Rule minCPSRule = isLeftClick ? 
                    ruleManager.getRule("combat.autoclicker.min_left_cps") : 
                    ruleManager.getRule("combat.autoclicker.min_right_cps");
            
            if (minCPSRule != null) {
                minCPS = minCPSRule.getValue();
            }
        }
        
        // Check if CPS is too high
        if (cps > maxCPS) {
            // Flag player for violation
            flag(player, (isLeftClick ? "Left" : "Right") + " CPS too high " + 
                    String.format("%.2f", cps) + " > " + String.format("%.2f", maxCPS));
            LoggerUtil.debug(player.getName() + " failed AutoClicker (" + 
                    (isLeftClick ? "Left" : "Right") + " CPS: " + String.format("%.2f", cps) + 
                    " > " + String.format("%.2f", maxCPS) + ")");
        }
        
        // Check if CPS is too low (potential macro)
        if (cps < minCPS && timestamps.size() >= 10) {
            // Flag player for violation
            flag(player, (isLeftClick ? "Left" : "Right") + " CPS too low (potential macro) " + 
                    String.format("%.2f", cps) + " < " + String.format("%.2f", minCPS));
            LoggerUtil.debug(player.getName() + " failed AutoClicker (" + 
                    (isLeftClick ? "Left" : "Right") + " CPS too low: " + 
                    String.format("%.2f", cps) + " < " + String.format("%.2f", minCPS) + ")");
        }
        
        // Check for consistent click patterns
        checkConsistentPattern(player, timestamps, isLeftClick);
    }
    
    /**
     * Checks for consistent click patterns
     * @param player The player
     * @param timestamps The click timestamps
     * @param isLeftClick Whether the clicks are left clicks
     */
    private void checkConsistentPattern(Player player, List<Long> timestamps, boolean isLeftClick) {
        // Calculate time differences between clicks
        List<Long> differences = new ArrayList<>();
        for (int i = 1; i < timestamps.size(); i++) {
            differences.add(timestamps.get(i) - timestamps.get(i - 1));
        }
        
        // Calculate standard deviation
        double mean = differences.stream().mapToLong(Long::longValue).average().orElse(0);
        double variance = differences.stream()
                .mapToDouble(diff -> Math.pow(diff - mean, 2))
                .average().orElse(0);
        double stdDev = Math.sqrt(variance);
        
        // Calculate coefficient of variation (CV)
        double cv = (mean > 0) ? stdDev / mean : 0;
        
        // Check for suspiciously consistent patterns (low CV)
        if (cv < 0.1 && differences.size() >= 10) {
            // Flag player for violation
            flag(player, (isLeftClick ? "Left" : "Right") + " click pattern too consistent (CV: " + 
                    String.format("%.2f", cv) + ")");
            LoggerUtil.debug(player.getName() + " failed AutoClicker (" + 
                    (isLeftClick ? "Left" : "Right") + " consistent pattern, CV: " + 
                    String.format("%.2f", cv) + ")");
        }
        
        // Check for double-click patterns
        checkDoubleClickPattern(player, differences, isLeftClick);
    }
    
    /**
     * Checks for double-click patterns
     * @param player The player
     * @param differences The time differences between clicks
     * @param isLeftClick Whether the clicks are left clicks
     */
    private void checkDoubleClickPattern(Player player, List<Long> differences, boolean isLeftClick) {
        // Count double-clicks (very short time between clicks)
        int doubleClicks = 0;
        for (long diff : differences) {
            if (diff < 10) { // Less than 10ms between clicks
                doubleClicks++;
            }
        }
        
        // Check if too many double-clicks
        if (doubleClicks > differences.size() / 3) { // More than 1/3 of clicks are double-clicks
            // Flag player for violation
            flag(player, (isLeftClick ? "Left" : "Right") + " click pattern has too many double-clicks (" + 
                    doubleClicks + "/" + differences.size() + ")");
            LoggerUtil.debug(player.getName() + " failed AutoClicker (" + 
                    (isLeftClick ? "Left" : "Right") + " double-clicks: " + 
                    doubleClicks + "/" + differences.size() + ")");
        }
    }
}