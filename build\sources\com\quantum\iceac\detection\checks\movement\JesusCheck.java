package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffectType;

import java.util.HashSet;
import java.util.Set;

/**
 * Check for Jesus (walking on liquids)
 */
public class JesusCheck extends Check {

    private static final Set<Material> LIQUID_MATERIALS = new HashSet<>();
    
    static {
        LIQUID_MATERIALS.add(Material.WATER);
        LIQUID_MATERIALS.add(Material.LAVA);
    }
    
    public JesusCheck(IceAC plugin) {
        super(plugin, "Jesus", CheckType.MOVEMENT);
        setDescription("Detects walking on liquids");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // Skip if player hasn't moved horizontally
        if (from.getX() == to.getX() && from.getZ() == to.getZ()) {
            return;
        }
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative or spectator mode
        if (player.getGameMode() == GameMode.CREATIVE || player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Skip if player has permission to fly
        if (player.isFlying() || player.getAllowFlight()) {
            return;
        }
        
        // Skip if player is gliding with elytra
        if (player.isGliding()) {
            return;
        }
        
        // Skip if player is in a vehicle
        if (player.isInsideVehicle()) {
            return;
        }
        
        // Skip if player has frost walker enchantment
        if (hasFrostWalker(player)) {
            return;
        }
        
        // Check if player is on liquid
        if (isOnLiquid(player) && !isNearSolid(player)) {
            // Check if player is moving horizontally on liquid
            double deltaX = to.getX() - from.getX();
            double deltaZ = to.getZ() - from.getZ();
            double horizontalDistance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
            
            // Check if player is moving too fast on liquid
            if (horizontalDistance > 0.1) {
                // Increment liquid time
                playerData.setLiquidTime(playerData.getLiquidTime() + 1);
                
                // Flag player after a few ticks to avoid false positives
                if (playerData.getLiquidTime() > 5) {
                    // Flag player for violation
                    flag(player, "Walking on liquid (Distance: " + 
                            String.format("%.2f", horizontalDistance) + ", Time: " + 
                            playerData.getLiquidTime() + ")");
                    LoggerUtil.debug(player.getName() + " failed Jesus (Distance: " + 
                            String.format("%.2f", horizontalDistance) + ", Time: " + 
                            playerData.getLiquidTime() + ")");
                }
            }
        } else {
            // Reset liquid time
            playerData.setLiquidTime(0);
        }
    }
    
    /**
     * Checks if a player is on a liquid block
     * @param player The player
     * @return True if player is on liquid, false otherwise
     */
    private boolean isOnLiquid(Player player) {
        Location location = player.getLocation();
        Block block = location.getBlock();
        Block blockBelow = block.getRelative(BlockFace.DOWN);
        
        // Check if player is standing on liquid
        if (LIQUID_MATERIALS.contains(blockBelow.getType())) {
            return true;
        }
        
        // Check if player is in liquid but not swimming
        if (LIQUID_MATERIALS.contains(block.getType())) {
            double yMod = location.getY() % 1.0;
            return yMod >= 0.0 && yMod <= 0.1; // Player is at the top of the liquid block
        }
        
        return false;
    }
    
    /**
     * Checks if a player is near a solid block
     * @param player The player
     * @return True if player is near a solid block, false otherwise
     */
    private boolean isNearSolid(Player player) {
        Location location = player.getLocation();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        
        // Check blocks around player
        for (int dx = -1; dx <= 1; dx++) {
            for (int dz = -1; dz <= 1; dz++) {
                // Skip center block
                if (dx == 0 && dz == 0) {
                    continue;
                }
                
                // Check if block is solid
                Block block = location.getWorld().getBlockAt(x + dx, y - 1, z + dz);
                if (block.getType().isSolid()) {
                    return true;
                }
                
                // Check block at player's level
                block = location.getWorld().getBlockAt(x + dx, y, z + dz);
                if (block.getType().isSolid()) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Checks if a player has frost walker enchantment on their boots
     * @param player The player
     * @return True if player has frost walker, false otherwise
     */
    private boolean hasFrostWalker(Player player) {
        ItemStack boots = player.getInventory().getBoots();
        if (boots == null) {
            return false;
        }
        
        // Check if boots have frost walker enchantment
        return boots.getEnchantments().keySet().stream()
                .anyMatch(enchantment -> enchantment.getName().contains("FROST_WALKER"));
    }
}