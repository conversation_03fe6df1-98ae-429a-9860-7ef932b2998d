@echo off
echo Testing Java compilation...

REM Create output directory
if not exist "build\test" mkdir "build\test"

REM Try to compile the main plugin class
echo Compiling IceAC.java...
javac -cp "gradle\wrapper\*;src\main\resources" -d "build\test" "src\main\java\com\quantum\iceac\IceAC.java" 2> compile-errors.txt

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: IceAC.java compiled successfully!
) else (
    echo ERROR: Compilation failed. Check compile-errors.txt
    type compile-errors.txt
)

pause
