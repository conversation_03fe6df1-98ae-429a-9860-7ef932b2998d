```
  ___            _    ____ 
 |_ _|___ ___   / \  / ___| 
  | |/ __/ _ \ / _ \| |    
  | | (_|  __// ___ \ |___ 
 |___\___\___/_/   \_\____|
```

# IceAC - Advanced Anti-Cheat System

IceAC is a comprehensive anti-cheat solution for Minecraft servers, designed to detect and prevent various types of cheating.

## Features

### Movement Checks
- **Speed**: Detects players moving faster than allowed
- **Fly**: Detects illegal flying
- **NoFall**: Detects players avoiding fall damage
- **Jesus**: Detects walking on liquids
- **Step**: Detects stepping up blocks higher than allowed

### Packet Checks
- **Timer**: Detects game speed manipulation
- **BadPackets**: Detects invalid or malicious packets
- **PingSpoof**: Detects ping manipulation

### Combat Checks
- **Reach**: Detects hitting entities from too far away
- **AutoClicker**: Detects automated clicking tools
- **Criticals**: Detects illegitimate critical hits

## Commands

- `/iceac help` - Shows help information
- `/iceac reload` - Reloads the plugin configuration
- `/iceac status` - Shows the status of all checks
- `/iceac alerts` - Toggles violation alerts
- `/iceac debug` - Toggles debug mode
- `/iceac whitelist <add|remove|list> [player]` - Manages the whitelist
- `/iceac add <player> <check> [amount] [reason]` - Manually adds violations to a player

## Permissions

- `iceac.admin` - Access to all commands
- `iceac.alerts` - Receive violation alerts
- `iceac.bypass` - Bypass all checks
- `iceac.bypass.<check>` - Bypass a specific check

## Configuration

The plugin is highly configurable through the `config.yml` file. You can adjust thresholds, enable/disable checks, and customize punishment actions.

## License

This project is licensed under the MIT License - see the LICENSE file for details.