package com.quantum.iceac.detection.checks.combat;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.potion.PotionEffectType;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for Criticals (triggering critical hits illegitimately)
 */
public class CriticalsCheck extends Check {

    private static final double MIN_FALL_DISTANCE = 0.1; // Minimum fall distance for a critical hit
    private static final double MAX_FALL_DISTANCE = 0.5; // Maximum fall distance for a legitimate critical hit
    
    private final Map<UUID, Boolean> wasOnGround = new HashMap<>();
    private final Map<UUID, Boolean> wasInAir = new HashMap<>();
    private final Map<UUID, Long> lastJumpTime = new HashMap<>();
    
    public CriticalsCheck(IceAC plugin) {
        super(plugin, "Criticals", CheckType.COMBAT);
        setDescription("Detects triggering critical hits illegitimately");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        UUID uuid = player.getUniqueId();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Check if player is on ground
        boolean onGround = isOnGround(player);
        boolean inAir = !onGround && player.getFallDistance() > 0;
        
        // Check for jump
        if (!wasOnGround.containsKey(uuid)) {
            wasOnGround.put(uuid, onGround);
            wasInAir.put(uuid, inAir);
        } else if (wasOnGround.get(uuid) && !onGround && !inAir) {
            // Player just jumped
            lastJumpTime.put(uuid, System.currentTimeMillis());
        }
        
        // Update ground state
        wasOnGround.put(uuid, onGround);
        wasInAir.put(uuid, inAir);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if damager is a player
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        Entity target = event.getEntity();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative or spectator mode
        if (player.getGameMode() == GameMode.CREATIVE || player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Skip if player has jump boost effect
        if (player.hasPotionEffect(PotionEffectType.JUMP)) {
            return;
        }
        
        // Check if attack is a critical hit
        if (isCriticalHit(player)) {
            // Check if critical hit is legitimate
            checkLegitCritical(player, target);
        }
    }
    
    /**
     * Checks if an attack is a critical hit
     * @param player The player
     * @return True if attack is a critical hit, false otherwise
     */
    private boolean isCriticalHit(Player player) {
        // Check conditions for a critical hit
        return player.getFallDistance() > MIN_FALL_DISTANCE && 
                !player.isOnGround() && 
                !player.isInsideVehicle() && 
                !player.hasPotionEffect(PotionEffectType.BLINDNESS) && 
                player.getLocation().getBlock().getType() != Material.LADDER && 
                player.getLocation().getBlock().getType() != Material.VINE && 
                player.getLocation().getBlock().getType() != Material.WATER && 
                player.getLocation().getBlock().getType() != Material.LAVA;
    }
    
    /**
     * Checks if a critical hit is legitimate
     * @param player The player
     * @param target The target entity
     */
    private void checkLegitCritical(Player player, Entity target) {
        UUID uuid = player.getUniqueId();
        
        // Check if player is in water or lava
        if (player.getLocation().getBlock().getType() == Material.WATER || 
                player.getLocation().getBlock().getType() == Material.LAVA) {
            // Flag player for violation
            flag(player, "Critical hit while in liquid");
            LoggerUtil.debug(player.getName() + " failed Criticals (In liquid)");
            return;
        }
        
        // Check if player is on ladder or vine
        if (player.getLocation().getBlock().getType() == Material.LADDER || 
                player.getLocation().getBlock().getType() == Material.VINE) {
            // Flag player for violation
            flag(player, "Critical hit while on ladder/vine");
            LoggerUtil.debug(player.getName() + " failed Criticals (On ladder/vine)");
            return;
        }
        
        // Check if player is in web
        if (player.getLocation().getBlock().getType() == Material.COBWEB) {
            // Flag player for violation
            flag(player, "Critical hit while in web");
            LoggerUtil.debug(player.getName() + " failed Criticals (In web)");
            return;
        }
        
        // Check if player has jump cooldown
        if (lastJumpTime.containsKey(uuid)) {
            long timeSinceJump = System.currentTimeMillis() - lastJumpTime.get(uuid);
            if (timeSinceJump < 100) { // Less than 100ms since jump
                // Flag player for violation
                flag(player, "Critical hit too soon after jump (" + timeSinceJump + "ms)");
                LoggerUtil.debug(player.getName() + " failed Criticals (Jump time: " + 
                        timeSinceJump + "ms)");
                return;
            }
        }
        
        // Check if fall distance is too high
        if (player.getFallDistance() > MAX_FALL_DISTANCE) {
            // Flag player for violation
            flag(player, "Critical hit with invalid fall distance (" + 
                    String.format("%.2f", player.getFallDistance()) + " > " + 
                    String.format("%.2f", MAX_FALL_DISTANCE) + ")");
            LoggerUtil.debug(player.getName() + " failed Criticals (Fall distance: " + 
                    String.format("%.2f", player.getFallDistance()) + " > " + 
                    String.format("%.2f", MAX_FALL_DISTANCE) + ")");
            return;
        }
        
        // Check if player is on ground according to our check
        if (wasOnGround.containsKey(uuid) && wasOnGround.get(uuid)) {
            // Flag player for violation
            flag(player, "Critical hit while on ground");
            LoggerUtil.debug(player.getName() + " failed Criticals (On ground)");
            return;
        }
    }
    
    /**
     * Checks if a player is on the ground
     * @param player The player
     * @return True if player is on ground, false otherwise
     */
    private boolean isOnGround(Player player) {
        Location location = player.getLocation();
        location.setY(location.getY() - 0.1);
        return location.getBlock().getType().isSolid();
    }
}