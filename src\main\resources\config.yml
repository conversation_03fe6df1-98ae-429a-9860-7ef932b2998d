# IceAC Configuration
# Revolutionary Anti-Cheat Protection for Minecraft servers

# General Settings
debug-mode: false
prefix: "&b[IceAC] &r"

# Detection Settings
detection:
  # Enable or disable the detection engine
  enabled: true
  
  # Neural network settings
  neural-network:
    enabled: true
    learning-rate: 0.01
    threshold: 0.85
  
  # Violation settings
  violations:
    # Reset violations after a certain time (in seconds)
    reset-time: 300
    # Maximum violations before punishment
    max-violations: 4

# Prevention Settings
prevention:
  # Enable or disable the prevention system
  enabled: true
  
  # Packet filtering settings
  packet-filtering:
    enabled: true
    # Maximum packets per second before throttling
    max-packets-per-second: 100
  
  # Rule adaptation settings
  rule-adaptation:
    enabled: true
    # How often to adapt rules (in minutes)
    adaptation-interval: 30

# Metrics Settings
metrics:
  # Enable or disable metrics collection
  enabled: true
  
  # Analytics dashboard settings
  dashboard:
    enabled: true
    # How often to update the dashboard (in seconds)
    update-interval: 5
  
  # Data storage settings
  storage:
    # How long to keep data (in days)
    retention-period: 7
    # How often to save data (in minutes)
    save-interval: 10

# Notification Settings
notifications:
  # Enable or disable notifications
  enabled: true
  
  # Discord webhook settings
  discord:
    enabled: false
    webhook-url: ""
    # Minimum violation level to send to Discord
    min-violation-level: 5
  
  # In-game notification settings
  in-game:
    enabled: true
    # Show detailed information in notifications
    detailed: true

# Performance Settings
performance:
  # Multi-threading settings
  multi-threading:
    enabled: true
    # Number of threads to use (0 = auto)
    thread-count: 0
  
  # Memory management settings
  memory-management:
    # How often to clean up memory (in minutes)
    cleanup-interval: 15
  
  # Network optimization settings
  network-optimization:
    enabled: true
    # Maximum latency compensation (in milliseconds)
    max-latency-compensation: 100

# Compatibility Settings
compatibility:
  # List of plugins to ignore
  ignored-plugins: []
  
  # Custom movement mechanics settings
  custom-movement:
    enabled: false
    # List of custom movement mechanics to allow
    allowed-mechanics: []