package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerMoveEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Check for InventoryMove (moving while inventory is open)
 */
public class InventoryMoveCheck extends Check {

    private static final double DEFAULT_MAX_INVENTORY_MOVE_DISTANCE = 0.1;
    private static final long DEFAULT_INVENTORY_MOVE_TIMEOUT = 500; // 500ms
    
    // Track inventory states
    private final Map<UUID, Boolean> inventoryOpen = new HashMap<>();
    private final Map<UUID, Long> inventoryOpenTime = new HashMap<>();
    private final Map<UUID, Integer> inventoryMoveViolations = new HashMap<>();
    
    public InventoryMoveCheck(IceAC plugin) {
        super(plugin, "InventoryMove", CheckType.MOVEMENT, "Detects moving while inventory is open");
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryOpen(InventoryOpenEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        inventoryOpen.put(uuid, true);
        inventoryOpenTime.put(uuid, System.currentTimeMillis());
        
        LoggerUtil.debug(player.getName() + " opened inventory");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getPlayer();
        UUID uuid = player.getUniqueId();
        
        inventoryOpen.put(uuid, false);
        inventoryOpenTime.remove(uuid);
        
        LoggerUtil.debug(player.getName() + " closed inventory");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        
        // Check if player has inventory open
        if (!inventoryOpen.getOrDefault(uuid, false)) {
            return;
        }
        
        // Check movement while inventory is open
        checkInventoryMovement(player, event);
    }
    
    /**
     * Checks movement while inventory is open
     * @param player The player
     * @param event The move event
     */
    private void checkInventoryMovement(Player player, PlayerMoveEvent event) {
        UUID uuid = player.getUniqueId();
        Long openTime = inventoryOpenTime.get(uuid);
        
        if (openTime == null) {
            return;
        }
        
        long now = System.currentTimeMillis();
        long inventoryOpenDuration = now - openTime;
        
        // Allow some time for the inventory to fully open
        long timeout = getPlugin().getConfig().getLong("checks.inventorymove.timeout", DEFAULT_INVENTORY_MOVE_TIMEOUT);
        if (inventoryOpenDuration < timeout) {
            return;
        }
        
        // Calculate movement distance
        double distance = event.getFrom().distance(event.getTo());
        double maxDistance = getPlugin().getConfig().getDouble("checks.inventorymove.max-distance", DEFAULT_MAX_INVENTORY_MOVE_DISTANCE);
        
        // Check if movement is too large
        if (distance > maxDistance) {
            int violations = inventoryMoveViolations.getOrDefault(uuid, 0) + 1;
            inventoryMoveViolations.put(uuid, violations);
            
            // Flag player for violation
            flag(player, "Moving while inventory open (Distance: " + String.format("%.3f", distance) + 
                    " > " + maxDistance + ", Duration: " + inventoryOpenDuration + "ms)");
            LoggerUtil.debug(player.getName() + " failed InventoryMove (Distance: " + 
                    String.format("%.3f", distance) + ", Violations: " + violations + ")");
            
            // Check for consistent violations
            if (violations > 5) {
                // Flag for pattern violation
                flag(player, "Consistent inventory movement violations (Count: " + violations + ")");
                LoggerUtil.debug(player.getName() + " failed InventoryMove (Pattern: " + violations + " violations)");
                
                // Reset counter
                inventoryMoveViolations.put(uuid, 0);
            }
        } else {
            // Reduce violations on good behavior
            int violations = inventoryMoveViolations.getOrDefault(uuid, 0);
            if (violations > 0) {
                inventoryMoveViolations.put(uuid, violations - 1);
            }
        }
        
        // Check for specific movement patterns
        checkMovementPatterns(player, event, inventoryOpenDuration);
    }
    
    /**
     * Checks for specific movement patterns while inventory is open
     * @param player The player
     * @param event The move event
     * @param inventoryOpenDuration How long inventory has been open
     */
    private void checkMovementPatterns(Player player, PlayerMoveEvent event, long inventoryOpenDuration) {
        // Check for sprinting while inventory is open
        if (player.isSprinting()) {
            // Flag player for violation
            flag(player, "Sprinting while inventory open (Duration: " + inventoryOpenDuration + "ms)");
            LoggerUtil.debug(player.getName() + " failed InventoryMove (Sprinting with inventory open)");
        }
        
        // Check for jumping while inventory is open
        if (event.getTo().getY() > event.getFrom().getY() + 0.1) {
            // Flag player for violation
            flag(player, "Jumping while inventory open (Y-diff: " + 
                    String.format("%.3f", event.getTo().getY() - event.getFrom().getY()) + ")");
            LoggerUtil.debug(player.getName() + " failed InventoryMove (Jumping with inventory open)");
        }
        
        // Check for rapid direction changes
        checkDirectionChanges(player, event);
    }
    
    /**
     * Checks for rapid direction changes while inventory is open
     * @param player The player
     * @param event The move event
     */
    private void checkDirectionChanges(Player player, PlayerMoveEvent event) {
        // Calculate direction change
        double fromX = event.getFrom().getX();
        double fromZ = event.getFrom().getZ();
        double toX = event.getTo().getX();
        double toZ = event.getTo().getZ();
        
        double deltaX = toX - fromX;
        double deltaZ = toZ - fromZ;
        
        // Check if there's significant movement
        if (Math.abs(deltaX) > 0.01 || Math.abs(deltaZ) > 0.01) {
            // Calculate movement angle
            double angle = Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI;
            
            // Store previous angle for comparison (simplified implementation)
            // In a real implementation, you would track this per player
            
            // For now, just check if movement is too precise (possible bot)
            if (Math.abs(deltaX) == Math.abs(deltaZ) && Math.abs(deltaX) > 0.05) {
                // Flag player for violation
                flag(player, "Precise diagonal movement while inventory open (Angle: " + 
                        String.format("%.1f", angle) + "°)");
                LoggerUtil.debug(player.getName() + " failed InventoryMove (Precise movement: " + 
                        String.format("%.1f", angle) + "°)");
            }
        }
    }
    
    /**
     * Checks if player is using inventory movement exploits
     * @param player The player
     */
    private void checkInventoryExploits(Player player) {
        UUID uuid = player.getUniqueId();
        
        // Check if inventory has been open for too long
        Long openTime = inventoryOpenTime.get(uuid);
        if (openTime != null) {
            long duration = System.currentTimeMillis() - openTime;
            
            // If inventory has been open for more than 30 seconds while moving
            if (duration > 30000) {
                int violations = inventoryMoveViolations.getOrDefault(uuid, 0);
                if (violations > 0) {
                    // Flag player for violation
                    flag(player, "Inventory open too long while moving (Duration: " + 
                            (duration / 1000) + "s, Violations: " + violations + ")");
                    LoggerUtil.debug(player.getName() + " failed InventoryMove (Long duration: " + 
                            (duration / 1000) + "s)");
                }
            }
        }
    }
    
    @Override
    public void onPlayerQuit(Player player) {
        UUID uuid = player.getUniqueId();
        inventoryOpen.remove(uuid);
        inventoryOpenTime.remove(uuid);
        inventoryMoveViolations.remove(uuid);
    }
}
