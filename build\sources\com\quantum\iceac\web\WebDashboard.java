package com.quantum.iceac.web;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;

/**
 * Web dashboard for monitoring IceAC in real-time
 */
public class WebDashboard {

    private final IceAC plugin;
    private HttpServer server;
    private boolean running = false;
    private int port;

    public WebDashboard(IceAC plugin) {
        this.plugin = plugin;
        this.port = plugin.getConfig().getInt("web-dashboard.port", 8080);
    }

    /**
     * Starts the web dashboard server
     */
    public void start() {
        if (!plugin.getConfig().getBoolean("web-dashboard.enabled", false)) {
            LoggerUtil.info("Web dashboard is disabled in configuration.");
            return;
        }

        try {
            server = HttpServer.create(new InetSocketAddress(port), 0);

            // Register endpoints
            server.createContext("/", new DashboardHandler());
            server.createContext("/api/stats", new StatsHandler());
            server.createContext("/api/players", new PlayersHandler());
            server.createContext("/api/violations", new ViolationsHandler());

            server.setExecutor(null);
            server.start();

            running = true;
            LoggerUtil.info("Web dashboard started on port " + port);
            LoggerUtil.info("Access dashboard at: http://localhost:" + port);

        } catch (IOException e) {
            LoggerUtil.severe("Failed to start web dashboard: " + e.getMessage());
        }
    }
    
    /**
     * Stops the web dashboard server
     */
    public void stop() {
        if (server != null && running) {
            server.stop(0);
            running = false;
            LoggerUtil.info("Web dashboard stopped.");
        }
    }
    
    /**
     * Checks if the dashboard is running
     * @return True if running
     */
    public boolean isRunning() {
        return running;
    }
    
    /**
     * Main dashboard page handler
     */
    private class DashboardHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = generateDashboardHTML();
            
            exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }
    }
    
    /**
     * Statistics API handler
     */
    private class StatsHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = generateStatsJSON();
            
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }
    }
    
    /**
     * Players API handler
     */
    private class PlayersHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = generatePlayersJSON();
            
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }
    }
    
    /**
     * Violations API handler
     */
    private class ViolationsHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = generateViolationsJSON();
            
            exchange.getResponseHeaders().set("Content-Type", "application/json");
            exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
            exchange.sendResponseHeaders(200, response.getBytes(StandardCharsets.UTF_8).length);
            
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes(StandardCharsets.UTF_8));
            }
        }
    }
    
    /**
     * Generates the main dashboard HTML
     * @return HTML content
     */
    private String generateDashboardHTML() {
        return """
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>IceAC Dashboard</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                    .container { max-width: 1200px; margin: 0 auto; }
                    .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                    .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
                    .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .stat-value { font-size: 2em; font-weight: bold; color: #3498db; }
                    .stat-label { color: #7f8c8d; margin-top: 5px; }
                    .players-table { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                    .table-header { background: #34495e; color: white; padding: 15px; font-weight: bold; }
                    .player-row { padding: 15px; border-bottom: 1px solid #ecf0f1; display: flex; justify-content: space-between; }
                    .player-row:last-child { border-bottom: none; }
                    .violation-count { background: #e74c3c; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
                    .status-online { color: #27ae60; font-weight: bold; }
                    .refresh-btn { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
                    .refresh-btn:hover { background: #2980b9; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>🧊 IceAC Dashboard</h1>
                        <p>Real-time Anti-Cheat Monitoring</p>
                        <button class="refresh-btn" onclick="refreshData()">Refresh Data</button>
                    </div>
                    
                    <div class="stats-grid" id="stats-grid">
                        <!-- Stats will be loaded here -->
                    </div>
                    
                    <div class="players-table">
                        <div class="table-header">Online Players</div>
                        <div id="players-list">
                            <!-- Players will be loaded here -->
                        </div>
                    </div>
                </div>
                
                <script>
                    function refreshData() {
                        loadStats();
                        loadPlayers();
                    }
                    
                    function loadStats() {
                        fetch('/api/stats')
                            .then(response => response.json())
                            .then(data => {
                                document.getElementById('stats-grid').innerHTML = `
                                    <div class="stat-card">
                                        <div class="stat-value">${data.onlinePlayers}</div>
                                        <div class="stat-label">Online Players</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-value">${data.totalViolations}</div>
                                        <div class="stat-label">Total Violations</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-value">${data.activeChecks}</div>
                                        <div class="stat-label">Active Checks</div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-value">${data.uptime}</div>
                                        <div class="stat-label">Uptime</div>
                                    </div>
                                `;
                            });
                    }
                    
                    function loadPlayers() {
                        fetch('/api/players')
                            .then(response => response.json())
                            .then(data => {
                                const playersHtml = data.players.map(player => `
                                    <div class="player-row">
                                        <span class="status-online">${player.name}</span>
                                        <span class="violation-count">${player.violations} violations</span>
                                    </div>
                                `).join('');
                                
                                document.getElementById('players-list').innerHTML = playersHtml || '<div class="player-row">No players online</div>';
                            });
                    }
                    
                    // Auto-refresh every 5 seconds
                    setInterval(refreshData, 5000);
                    
                    // Initial load
                    refreshData();
                </script>
            </body>
            </html>
            """;
    }

    /**
     * Generates statistics JSON
     * @return JSON string
     */
    private String generateStatsJSON() {
        int onlinePlayers = Bukkit.getOnlinePlayers().size();
        int totalViolations = 0;
        int activeChecks = plugin.getDetectionEngine().getCheckManager().getChecks().size();

        // Calculate total violations
        for (Player player : Bukkit.getOnlinePlayers()) {
            PlayerData data = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
            if (data != null) {
                // Sum all violations for this player
                for (String checkName : plugin.getDetectionEngine().getCheckManager().getChecks().stream()
                        .map(check -> check.getName()).toList()) {
                    totalViolations += data.getViolations(checkName);
                }
            }
        }

        long uptimeMs = System.currentTimeMillis() - plugin.getMetricsManager().getStartTime();
        String uptime = formatUptime(uptimeMs);

        return String.format("""
            {
                "onlinePlayers": %d,
                "totalViolations": %d,
                "activeChecks": %d,
                "uptime": "%s"
            }
            """, onlinePlayers, totalViolations, activeChecks, uptime);
    }

    /**
     * Generates players JSON
     * @return JSON string
     */
    private String generatePlayersJSON() {
        StringBuilder json = new StringBuilder();
        json.append("{\"players\":[");

        boolean first = true;
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (!first) json.append(",");
            first = false;

            PlayerData data = plugin.getDetectionEngine().getPlayerDataManager().getPlayerData(player);
            int totalViolations = 0;

            if (data != null) {
                for (String checkName : plugin.getDetectionEngine().getCheckManager().getChecks().stream()
                        .map(check -> check.getName()).toList()) {
                    totalViolations += data.getViolations(checkName);
                }
            }

            json.append(String.format("""
                {"name":"%s","violations":%d}
                """, player.getName(), totalViolations));
        }

        json.append("]}");
        return json.toString();
    }

    /**
     * Generates violations JSON
     * @return JSON string
     */
    private String generateViolationsJSON() {
        // This would contain recent violations data
        return "{\"violations\":[]}";
    }

    /**
     * Formats uptime in a readable format
     * @param uptimeMs Uptime in milliseconds
     * @return Formatted uptime string
     */
    private String formatUptime(long uptimeMs) {
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return days + "d " + (hours % 24) + "h";
        } else if (hours > 0) {
            return hours + "h " + (minutes % 60) + "m";
        } else {
            return minutes + "m " + (seconds % 60) + "s";
        }
    }
}
