plugins {
    id 'java'
    id 'com.github.johnrengelman.shadow' version '7.1.2'
}

group = 'com.quantum'
version = '1.0.0'

repositories {
    mavenCentral()
    maven { url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/' }
    maven { url = 'https://repo.dmulloy2.net/repository/public/' } // ProtocolLib
    maven { url = 'https://jitpack.io' }
}

dependencies {
    compileOnly 'org.spigotmc:spigot-api:1.19-R0.1-SNAPSHOT'
    compileOnly 'com.comphenix.protocol:ProtocolLib:4.7.0'
    
    implementation 'org.bstats:bstats-bukkit:3.0.0'
    
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.0'
}

shadowJar {
    archiveClassifier.set('')
    relocate 'org.bstats', 'com.quantum.iceac.libs.bstats'
}

build {
    dependsOn shadowJar
}

compileJava {
    options.encoding = 'UTF-8'
    options.release = 17
}

processResources {
    filesMatching('plugin.yml') {
        expand(project.properties)
    }
}

test {
    useJUnitPlatform()
}