package com.quantum.iceac.detection.data;

import org.bukkit.entity.Player;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Stores data for an individual player
 */
public class PlayerData implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private final UUID playerUUID;
    private final String playerName;
    private final Map<String, Integer> violations;
    private final Map<String, Long> lastViolationTime;
    private boolean exempt;
    
    // Movement data
    private double lastX, lastY, lastZ;
    private float lastYaw, lastPitch;
    private boolean onGround;
    private long lastMovementTime;
    
    // Combat data
    private long lastAttackTime;
    private int attackCount;
    private long attackCountResetTime;
    
    // Packet data
    private int packetCount;
    private long packetCountResetTime;
    
    // Transient data (not serialized)
    private transient Player player;
    
    /**
     * Creates a new player data instance
     * @param player The player
     */
    public PlayerData(Player player) {
        this.player = player;
        this.playerUUID = player.getUniqueId();
        this.playerName = player.getName();
        this.violations = new HashMap<>();
        this.lastViolationTime = new HashMap<>();
        this.exempt = false;
        
        // Initialize movement data
        this.lastX = player.getLocation().getX();
        this.lastY = player.getLocation().getY();
        this.lastZ = player.getLocation().getZ();
        this.lastYaw = player.getLocation().getYaw();
        this.lastPitch = player.getLocation().getPitch();
        this.onGround = player.isOnGround();
        this.lastMovementTime = System.currentTimeMillis();
        
        // Initialize combat data
        this.lastAttackTime = 0;
        this.attackCount = 0;
        this.attackCountResetTime = System.currentTimeMillis() + 1000;
        
        // Initialize packet data
        this.packetCount = 0;
        this.packetCountResetTime = System.currentTimeMillis() + 1000;
    }
    
    /**
     * Gets the player UUID
     * @return The player UUID
     */
    public UUID getPlayerUUID() {
        return playerUUID;
    }
    
    /**
     * Gets the player name
     * @return The player name
     */
    public String getPlayerName() {
        return playerName;
    }
    
    /**
     * Gets the player
     * @return The player
     */
    public Player getPlayer() {
        return player;
    }
    
    /**
     * Sets the player
     * @param player The player
     */
    public void setPlayer(Player player) {
        this.player = player;
    }
    
    /**
     * Checks if the player is exempt from checks
     * @return True if exempt, false otherwise
     */
    public boolean isExempt() {
        return exempt;
    }
    
    /**
     * Sets whether the player is exempt from checks
     * @param exempt True if exempt, false otherwise
     */
    public void setExempt(boolean exempt) {
        this.exempt = exempt;
    }
    
    /**
     * Gets the number of violations for a check
     * @param checkName The name of the check
     * @return The number of violations
     */
    public int getViolations(String checkName) {
        return violations.getOrDefault(checkName, 0);
    }
    
    /**
     * Adds a violation for a check
     * @param checkName The name of the check
     * @return The new number of violations
     */
    public int addViolation(String checkName) {
        int count = getViolations(checkName) + 1;
        violations.put(checkName, count);
        lastViolationTime.put(checkName, System.currentTimeMillis());
        return count;
    }
    
    /**
     * Resets violations for a check
     * @param checkName The name of the check
     */
    public void resetViolations(String checkName) {
        violations.put(checkName, 0);
    }
    
    /**
     * Gets the last violation time for a check
     * @param checkName The name of the check
     * @return The last violation time, or 0 if none
     */
    public long getLastViolationTime(String checkName) {
        return lastViolationTime.getOrDefault(checkName, 0L);
    }
    
    /**
     * Updates movement data
     * @param x The x coordinate
     * @param y The y coordinate
     * @param z The z coordinate
     * @param yaw The yaw
     * @param pitch The pitch
     * @param onGround Whether the player is on the ground
     */
    public void updateMovement(double x, double y, double z, float yaw, float pitch, boolean onGround) {
        this.lastX = x;
        this.lastY = y;
        this.lastZ = z;
        this.lastYaw = yaw;
        this.lastPitch = pitch;
        this.onGround = onGround;
        this.lastMovementTime = System.currentTimeMillis();
    }
    
    /**
     * Gets the last x coordinate
     * @return The last x coordinate
     */
    public double getLastX() {
        return lastX;
    }
    
    /**
     * Gets the last y coordinate
     * @return The last y coordinate
     */
    public double getLastY() {
        return lastY;
    }
    
    /**
     * Gets the last z coordinate
     * @return The last z coordinate
     */
    public double getLastZ() {
        return lastZ;
    }
    
    /**
     * Gets the last yaw
     * @return The last yaw
     */
    public float getLastYaw() {
        return lastYaw;
    }
    
    /**
     * Gets the last pitch
     * @return The last pitch
     */
    public float getLastPitch() {
        return lastPitch;
    }
    
    /**
     * Checks if the player is on the ground
     * @return True if on the ground, false otherwise
     */
    public boolean isOnGround() {
        return onGround;
    }
    
    /**
     * Gets the last movement time
     * @return The last movement time
     */
    public long getLastMovementTime() {
        return lastMovementTime;
    }
    
    /**
     * Records an attack
     */
    public void recordAttack() {
        long now = System.currentTimeMillis();
        this.lastAttackTime = now;
        
        // Reset attack count if needed
        if (now > attackCountResetTime) {
            attackCount = 0;
            attackCountResetTime = now + 1000;
        }
        
        // Increment attack count
        attackCount++;
    }
    
    /**
     * Gets the last attack time
     * @return The last attack time
     */
    public long getLastAttackTime() {
        return lastAttackTime;
    }
    
    /**
     * Gets the attack count
     * @return The attack count
     */
    public int getAttackCount() {
        // Reset attack count if needed
        long now = System.currentTimeMillis();
        if (now > attackCountResetTime) {
            attackCount = 0;
            attackCountResetTime = now + 1000;
        }
        
        return attackCount;
    }
    
    /**
     * Records a packet
     */
    public void recordPacket() {
        long now = System.currentTimeMillis();
        
        // Reset packet count if needed
        if (now > packetCountResetTime) {
            packetCount = 0;
            packetCountResetTime = now + 1000;
        }
        
        // Increment packet count
        packetCount++;
    }
    
    /**
     * Gets the packet count
     * @return The packet count
     */
    public int getPacketCount() {
        // Reset packet count if needed
        long now = System.currentTimeMillis();
        if (now > packetCountResetTime) {
            packetCount = 0;
            packetCountResetTime = now + 1000;
        }
        
        return packetCount;
    }
    
    /**
     * Saves player data to a file
     * @param file The file to save to
     * @throws IOException If an I/O error occurs
     */
    public void saveToFile(File file) throws IOException {
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(file))) {
            oos.writeObject(this);
        }
    }
    
    /**
     * Loads player data from a file
     * @param file The file to load from
     * @throws IOException If an I/O error occurs
     */
    public void loadFromFile(File file) throws IOException {
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(file))) {
            PlayerData data = (PlayerData) ois.readObject();
            
            // Copy data from loaded object
            this.violations.clear();
            this.violations.putAll(data.violations);
            
            this.lastViolationTime.clear();
            this.lastViolationTime.putAll(data.lastViolationTime);
            
            this.exempt = data.exempt;
        } catch (ClassNotFoundException e) {
            throw new IOException("Failed to load player data", e);
        }
    }
}