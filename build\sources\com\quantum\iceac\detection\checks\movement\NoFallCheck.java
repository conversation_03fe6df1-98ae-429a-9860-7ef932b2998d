package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDamageEvent.DamageCause;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.potion.PotionEffectType;

/**
 * Check for NoFall (avoiding fall damage)
 */
public class NoFallCheck extends Check {

    private static final double FALL_DAMAGE_THRESHOLD = 3.0; // Blocks needed to start taking fall damage
    
    public NoFallCheck(IceAC plugin) {
        super(plugin, "NoFall", CheckType.MOVEMENT);
        setDescription("Detects avoiding fall damage");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative or spectator mode
        if (player.getGameMode() == GameMode.CREATIVE || player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Skip if player has permission to fly
        if (player.isFlying() || player.getAllowFlight()) {
            return;
        }
        
        // Skip if player is gliding with elytra
        if (player.isGliding()) {
            return;
        }
        
        // Skip if player is in a vehicle
        if (player.isInsideVehicle()) {
            return;
        }
        
        // Skip if player has slow falling effect
        if (player.hasPotionEffect(PotionEffectType.SLOW_FALLING)) {
            return;
        }
        
        // Skip if player is in water or lava
        if (player.getLocation().getBlock().getType() == Material.WATER || 
                player.getLocation().getBlock().getType() == Material.LAVA) {
            playerData.setFallDistance(0);
            return;
        }
        
        // Skip if player is on a ladder or vine
        if (player.getLocation().getBlock().getType() == Material.LADDER || 
                player.getLocation().getBlock().getType() == Material.VINE) {
            playerData.setFallDistance(0);
            return;
        }
        
        // Skip if player is in web
        if (player.getLocation().getBlock().getType() == Material.COBWEB) {
            playerData.setFallDistance(0);
            return;
        }
        
        // Check for ground contact
        boolean onGround = isOnGround(player);
        boolean wasOnGround = playerData.isOnGround();
        playerData.setOnGround(onGround);
        
        // Update fall distance
        double deltaY = to.getY() - from.getY();
        
        if (deltaY < 0 && !onGround) {
            // Player is falling
            playerData.setFallDistance(playerData.getFallDistance() + Math.abs(deltaY));
        } else if (onGround && !wasOnGround) {
            // Player has landed
            checkFallDamage(player, playerData);
            playerData.setFallDistance(0);
        } else if (deltaY > 0) {
            // Player is moving upward
            playerData.setFallDistance(0);
        }
        
        // Check for suspicious ground state
        checkGroundState(player, playerData, onGround);
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDamage(EntityDamageEvent event) {
        // Check if entity is a player
        if (!(event.getEntity() instanceof Player)) {
            return;
        }
        
        // Check if damage is from falling
        if (event.getCause() != DamageCause.FALL) {
            return;
        }
        
        Player player = (Player) event.getEntity();
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Check if player should have taken fall damage
        if (playerData.getFallDistance() > FALL_DAMAGE_THRESHOLD && event.isCancelled()) {
            // Flag player for violation
            flag(player, "Cancelled fall damage (Fall distance: " + 
                    String.format("%.2f", playerData.getFallDistance()) + ")");
            LoggerUtil.debug(player.getName() + " failed NoFall (Cancelled damage, Fall distance: " + 
                    String.format("%.2f", playerData.getFallDistance()) + ")");
        }
        
        // Reset fall distance
        playerData.setFallDistance(0);
    }
    
    /**
     * Checks if a player should have taken fall damage
     * @param player The player
     * @param playerData The player data
     */
    private void checkFallDamage(Player player, PlayerData playerData) {
        // Check if player should have taken fall damage
        if (playerData.getFallDistance() > FALL_DAMAGE_THRESHOLD && player.getFallDistance() < 1.0) {
            // Flag player for violation
            flag(player, "Avoided fall damage (Fall distance: " + 
                    String.format("%.2f", playerData.getFallDistance()) + ", Bukkit: " + 
                    String.format("%.2f", player.getFallDistance()) + ")");
            LoggerUtil.debug(player.getName() + " failed NoFall (Avoided damage, Fall distance: " + 
                    String.format("%.2f", playerData.getFallDistance()) + ", Bukkit: " + 
                    String.format("%.2f", player.getFallDistance()) + ")");
        }
    }
    
    /**
     * Checks for suspicious ground state
     * @param player The player
     * @param playerData The player data
     * @param onGround Whether the player is on ground
     */
    private void checkGroundState(Player player, PlayerData playerData, boolean onGround) {
        // Check if player's ground state is different from Bukkit's
        boolean bukkitOnGround = player.isOnGround();
        
        if (onGround != bukkitOnGround && playerData.getFallDistance() > FALL_DAMAGE_THRESHOLD) {
            // Flag player for violation
            flag(player, "Suspicious ground state (Our: " + onGround + ", Bukkit: " + bukkitOnGround + 
                    ", Fall distance: " + String.format("%.2f", playerData.getFallDistance()) + ")");
            LoggerUtil.debug(player.getName() + " failed NoFall (Ground state, Our: " + onGround + 
                    ", Bukkit: " + bukkitOnGround + ", Fall distance: " + 
                    String.format("%.2f", playerData.getFallDistance()) + ")");
        }
    }
    
    /**
     * Checks if a player is on the ground
     * @param player The player
     * @return True if player is on ground, false otherwise
     */
    private boolean isOnGround(Player player) {
        Location location = player.getLocation();
        location.setY(location.getY() - 0.1);
        return location.getBlock().getType().isSolid();
    }
}