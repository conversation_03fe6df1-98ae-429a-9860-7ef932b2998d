@echo off
echo Creating IceAC JAR manually...

REM Create libs directory
if not exist "build\libs" mkdir "build\libs"

REM Copy resources to classes directory
xcopy "src\main\resources\*" "build\classes\java\main\" /E /Y

REM Create JAR file
cd build\classes\java\main
jar -cf "..\..\..\libs\IceAC-1.0.0-MINIMAL.jar" *
cd ..\..\..\..

echo.
echo JAR created successfully!
echo Location: build\libs\IceAC-1.0.0-MINIMAL.jar
echo.

REM Show JAR contents
echo JAR Contents:
jar -tf "build\libs\IceAC-1.0.0-MINIMAL.jar"

echo.
echo Build completed successfully!
pause
