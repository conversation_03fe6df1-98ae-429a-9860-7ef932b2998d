package com.quantum.iceac.metrics;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Collects metrics data for the anti-cheat system
 */
public class MetricsCollector {

    private final IceAC plugin;
    private final Map<String, Integer> checkViolations;
    private final Map<UUID, Boolean> alertsEnabled;
    private int totalViolations;
    private int totalPunishments;
    private long startTime;
    
    public MetricsCollector(IceAC plugin) {
        this.plugin = plugin;
        this.checkViolations = new HashMap<>();
        this.alertsEnabled = new HashMap<>();
        this.totalViolations = 0;
        this.totalPunishments = 0;
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * Initializes the metrics collector
     */
    public void initialize() {
        // Load metrics data from file if it exists
        File metricsFile = new File(plugin.getDataFolder(), "metrics/data.yml");
        if (metricsFile.exists()) {
            YamlConfiguration config = YamlConfiguration.loadConfiguration(metricsFile);
            
            // Load check violations
            if (config.contains("check-violations")) {
                for (String check : config.getConfigurationSection("check-violations").getKeys(false)) {
                    checkViolations.put(check, config.getInt("check-violations." + check));
                }
            }
            
            // Load total violations and punishments
            totalViolations = config.getInt("total-violations", 0);
            totalPunishments = config.getInt("total-punishments", 0);
            
            LoggerUtil.debug("Loaded metrics data from file.");
        }
        
        // Reset start time
        startTime = System.currentTimeMillis();
        
        LoggerUtil.info("Initialized metrics collector.");
    }
    
    /**
     * Records a violation for a check
     * @param checkName The name of the check
     */
    public void recordViolation(String checkName) {
        // Increment check violations
        checkViolations.put(checkName, checkViolations.getOrDefault(checkName, 0) + 1);
        
        // Increment total violations
        totalViolations++;
    }
    
    /**
     * Records a punishment
     */
    public void recordPunishment() {
        // Increment total punishments
        totalPunishments++;
    }
    
    /**
     * Gets the number of violations for a check
     * @param checkName The name of the check
     * @return The number of violations
     */
    public int getViolations(String checkName) {
        return checkViolations.getOrDefault(checkName, 0);
    }
    
    /**
     * Gets the total number of violations
     * @return The total number of violations
     */
    public int getTotalViolations() {
        return totalViolations;
    }
    
    /**
     * Gets the total number of punishments
     * @return The total number of punishments
     */
    public int getTotalPunishments() {
        return totalPunishments;
    }
    
    /**
     * Gets the uptime of the anti-cheat system
     * @return The uptime in milliseconds
     */
    public long getUptime() {
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * Checks if alerts are enabled for a player
     * @param uuid The UUID of the player
     * @return True if alerts are enabled, false otherwise
     */
    public boolean areAlertsEnabled(UUID uuid) {
        return alertsEnabled.getOrDefault(uuid, false);
    }
    
    /**
     * Sets whether alerts are enabled for a player
     * @param uuid The UUID of the player
     * @param enabled True if alerts are enabled, false otherwise
     */
    public void setAlertsEnabled(UUID uuid, boolean enabled) {
        alertsEnabled.put(uuid, enabled);
    }
    
    /**
     * Saves metrics data to a file
     */
    public void saveData() {
        // Create metrics directory if it doesn't exist
        File metricsDir = new File(plugin.getDataFolder(), "metrics");
        if (!metricsDir.exists()) {
            metricsDir.mkdirs();
        }
        
        // Create metrics file
        File metricsFile = new File(metricsDir, "data.yml");
        YamlConfiguration config = new YamlConfiguration();
        
        // Save check violations
        for (Map.Entry<String, Integer> entry : checkViolations.entrySet()) {
            config.set("check-violations." + entry.getKey(), entry.getValue());
        }
        
        // Save total violations and punishments
        config.set("total-violations", totalViolations);
        config.set("total-punishments", totalPunishments);
        
        // Save file
        try {
            config.save(metricsFile);
            LoggerUtil.debug("Saved metrics data to file.");
        } catch (IOException e) {
            LoggerUtil.severe("Failed to save metrics data to file", e);
        }
    }
}