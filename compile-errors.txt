src\main\java\com\quantum\iceac\IceAC.java:3: error: package com.quantum.iceac.commands does not exist
import com.quantum.iceac.commands.CommandManager;
                                 ^
src\main\java\com\quantum\iceac\IceAC.java:4: error: package com.quantum.iceac.config does not exist
import com.quantum.iceac.config.ConfigManager;
                               ^
src\main\java\com\quantum\iceac\IceAC.java:5: error: package com.quantum.iceac.detection does not exist
import com.quantum.iceac.detection.DetectionEngine;
                                  ^
src\main\java\com\quantum\iceac\IceAC.java:6: error: package com.quantum.iceac.listeners does not exist
import com.quantum.iceac.listeners.PlayerListener;
                                  ^
src\main\java\com\quantum\iceac\IceAC.java:7: error: package com.quantum.iceac.metrics does not exist
import com.quantum.iceac.metrics.MetricsManager;
                                ^
src\main\java\com\quantum\iceac\IceAC.java:8: error: package com.quantum.iceac.prevention does not exist
import com.quantum.iceac.prevention.PreventionSystem;
                                   ^
src\main\java\com\quantum\iceac\IceAC.java:9: error: package com.quantum.iceac.utils does not exist
import com.quantum.iceac.utils.LoggerUtil;
                              ^
src\main\java\com\quantum\iceac\IceAC.java:10: error: package org.bstats.bukkit does not exist
import org.bstats.bukkit.Metrics;
                        ^
src\main\java\com\quantum\iceac\IceAC.java:11: error: package org.bukkit does not exist
import org.bukkit.Bukkit;
                 ^
src\main\java\com\quantum\iceac\IceAC.java:12: error: cannot find symbol
import org.bukkit.plugin.java.JavaPlugin;
                             ^
  symbol:   class JavaPlugin
  location: package org.bukkit.plugin.java
src\main\java\com\quantum\iceac\IceAC.java:18: error: cannot find symbol
public class IceAC extends JavaPlugin {
                           ^
  symbol: class JavaPlugin
src\main\java\com\quantum\iceac\IceAC.java:21: error: cannot find symbol
    private ConfigManager configManager;
            ^
  symbol:   class ConfigManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:22: error: cannot find symbol
    private DetectionEngine detectionEngine;
            ^
  symbol:   class DetectionEngine
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:23: error: cannot find symbol
    private PreventionSystem preventionSystem;
            ^
  symbol:   class PreventionSystem
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:24: error: cannot find symbol
    private MetricsManager metricsManager;
            ^
  symbol:   class MetricsManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:25: error: cannot find symbol
    private CommandManager commandManager;
            ^
  symbol:   class CommandManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:131: error: cannot find symbol
    public ConfigManager getConfigManager() {
           ^
  symbol:   class ConfigManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:139: error: cannot find symbol
    public DetectionEngine getDetectionEngine() {
           ^
  symbol:   class DetectionEngine
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:147: error: cannot find symbol
    public PreventionSystem getPreventionSystem() {
           ^
  symbol:   class PreventionSystem
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:155: error: cannot find symbol
    public MetricsManager getMetricsManager() {
           ^
  symbol:   class MetricsManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:27: error: method does not override or implement a method from a supertype
    @Override
    ^
src\main\java\com\quantum\iceac\IceAC.java:32: error: cannot find symbol
        LoggerUtil.init(this);
        ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:33: error: cannot find symbol
        LoggerUtil.info("Initializing IceAC Anti-Cheat Protection...");
        ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:37: error: cannot find symbol
            LoggerUtil.severe("Required dependencies not found. Disabling plugin.");
            ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:38: error: cannot find symbol
            getServer().getPluginManager().disablePlugin(this);
            ^
  symbol:   method getServer()
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:49: error: cannot find symbol
        commandManager = new CommandManager(this);
                             ^
  symbol:   class CommandManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:55: error: cannot find symbol
        LoggerUtil.info("IceAC has been successfully enabled!");
        ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:56: error: cannot find symbol
        LoggerUtil.info("Running on version " + getDescription().getVersion());
                                                ^
  symbol:   method getDescription()
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:56: error: cannot find symbol
        LoggerUtil.info("Running on version " + getDescription().getVersion());
        ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:59: error: method does not override or implement a method from a supertype
    @Override
    ^
src\main\java\com\quantum\iceac\IceAC.java:61: error: cannot find symbol
        LoggerUtil.info("Shutting down IceAC...");
        ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:80: error: cannot find symbol
        LoggerUtil.info("IceAC has been successfully disabled!");
        ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:84: error: cannot find symbol
        if (Bukkit.getPluginManager().getPlugin("ProtocolLib") == null) {
            ^
  symbol:   variable Bukkit
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:85: error: cannot find symbol
            LoggerUtil.severe("ProtocolLib is required for IceAC to function properly!");
            ^
  symbol:   variable LoggerUtil
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:93: error: cannot find symbol
        configManager = new ConfigManager(this);
                            ^
  symbol:   class ConfigManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:97: error: cannot find symbol
        detectionEngine = new DetectionEngine(this);
                              ^
  symbol:   class DetectionEngine
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:101: error: cannot find symbol
        preventionSystem = new PreventionSystem(this);
                               ^
  symbol:   class PreventionSystem
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:105: error: cannot find symbol
        metricsManager = new MetricsManager(this);
                             ^
  symbol:   class MetricsManager
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:110: error: cannot find symbol
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
                                                          ^
  symbol:   class PlayerListener
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:110: error: cannot find symbol
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        ^
  symbol:   method getServer()
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:116: error: cannot find symbol
        Metrics metrics = new Metrics(this, pluginId);
        ^
  symbol:   class Metrics
  location: class IceAC
src\main\java\com\quantum\iceac\IceAC.java:116: error: cannot find symbol
        Metrics metrics = new Metrics(this, pluginId);
                              ^
  symbol:   class Metrics
  location: class IceAC
42 errors
