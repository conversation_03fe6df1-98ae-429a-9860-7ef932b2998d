package com.quantum.iceac.utils;

import com.quantum.iceac.IceAC;

import java.util.logging.Level;

/**
 * Utility class for logging messages to the console
 */
public class LoggerUtil {

    private static IceAC plugin;
    private static boolean debugMode = false;
    
    /**
     * Initializes the logger utility
     * @param plugin The plugin instance
     */
    public static void init(IceAC plugin) {
        LoggerUtil.plugin = plugin;
        reloadDebugMode();
    }
    
    /**
     * Reloads the debug mode setting from the configuration
     */
    public static void reloadDebugMode() {
        if (plugin != null && plugin.getConfig() != null) {
            debugMode = plugin.getConfig().getBoolean("debug-mode", false);
        }
    }
    
    /**
     * Logs an info message to the console
     * @param message The message to log
     */
    public static void info(String message) {
        if (plugin != null) {
            plugin.getLogger().info(message);
        }
    }
    
    /**
     * Logs a warning message to the console
     * @param message The message to log
     */
    public static void warning(String message) {
        if (plugin != null) {
            plugin.getLogger().warning(message);
        }
    }
    
    /**
     * Logs a severe message to the console
     * @param message The message to log
     */
    public static void severe(String message) {
        if (plugin != null) {
            plugin.getLogger().severe(message);
        }
    }
    
    /**
     * Logs a severe message to the console with an exception
     * @param message The message to log
     * @param throwable The exception to log
     */
    public static void severe(String message, Throwable throwable) {
        if (plugin != null) {
            plugin.getLogger().log(Level.SEVERE, message, throwable);
        }
    }
    
    /**
     * Logs a debug message to the console if debug mode is enabled
     * @param message The message to log
     */
    public static void debug(String message) {
        if (debugMode && plugin != null) {
            plugin.getLogger().info("[DEBUG] " + message);
        }
    }
}