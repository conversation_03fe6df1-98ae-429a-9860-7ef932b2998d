# IceAC Checks Configuration
# This file contains all the cheat detection checks and their settings

# General settings for all checks
general:
  # Whether to use the neural network for detection
  use-neural-network: true
  
  # Whether to adapt check thresholds based on server conditions
  adaptive-thresholds: true
  
  # Whether to log check violations to a file
  log-violations: true
  
  # Whether to notify staff of check violations
  notify-staff: true

# Combat checks
combat:
  # KillAura detection
  killaura:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cKillAura§8)"
    description: "Detects automated combat hacks"
    
    # Sub-checks
    angle: true
    pattern: true
    rotation: true
    accuracy: true
    multi-aura: true
  
  # Reach detection
  reach:
    enabled: true
    violation-threshold: 20
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cReach§8)"
    description: "Detects hitting entities from too far away"
    
    # Maximum reach distance
    max-reach: 3.0
    
    # Whether to account for latency
    account-for-latency: true
  
  # AutoClicker detection
  autoclicker:
    enabled: true
    violation-threshold: 25
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cAutoClicker§8)"
    description: "Detects automated clicking tools"
    
    # Maximum clicks per second
    max-cps: 20
    
    # Minimum clicks per second to trigger detection
    min-cps: 12
    
    # Whether to check for consistent patterns
    check-patterns: true
  
  # Criticals detection
  criticals:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cCriticals§8)"
    description: "Detects forced critical hits"

  # Aim detection
  aim:
    enabled: true
    violation-threshold: 20
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cAimbot§8)"
    description: "Detects aim assistance and aimbot"

    # Maximum aim consistency (0.0 to 1.0)
    max-consistency: 0.95

    # Minimum aim variation required
    min-variation: 0.1

    # Suspicious snap angle threshold
    suspicious-snap-angle: 45.0

  # Velocity/Knockback detection
  velocity:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cVelocity§8)"
    description: "Detects velocity/knockback modifications"

    # Minimum velocity ratio (anti-knockback detection)
    min-ratio: 0.7

    # Maximum velocity ratio (velocity modifier detection)
    max-ratio: 1.3

    # Maximum angle difference for direction check
    max-angle: 45.0

    # Velocity timeout in milliseconds
    timeout: 2000

# Movement checks
movement:
  # Speed detection
  speed:
    enabled: true
    violation-threshold: 25
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cSpeed§8)"
    description: "Detects moving faster than allowed"
    
    # Maximum speed multiplier
    max-speed-multiplier: 1.1
    
    # Whether to check for speed in vehicles
    check-vehicles: true
  
  # Fly detection
  fly:
    enabled: true
    violation-threshold: 20
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cFly§8)"
    description: "Detects unauthorized flying"
    
    # Whether to check for elytra exploits
    check-elytra: true
    
    # Whether to check for levitation exploits
    check-levitation: true
  
  # NoFall detection
  nofall:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cNoFall§8)"
    description: "Detects fall damage avoidance"
  
  # Jesus/WaterWalk detection
  jesus:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cJesus§8)"
    description: "Detects walking on liquids"
  
  # Step/Spider detection
  step:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cStep§8)"
    description: "Detects climbing walls or stepping up blocks too high"

    # Maximum step height
    max-step-height: 0.6

  # Scaffold detection
  scaffold:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cScaffold§8)"
    description: "Detects automatic block placement while moving"

    # Maximum blocks per second
    max-blocks-per-second: 10

    # Maximum movement speed while scaffolding
    max-speed: 0.2

    # Minimum rotation change required
    min-rotation-change: 5.0

  # InventoryMove detection
  inventorymove:
    enabled: true
    violation-threshold: 10
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cInventoryMove§8)"
    description: "Detects moving while inventory is open"

    # Maximum movement distance while inventory is open
    max-distance: 0.1

    # Timeout before checking starts (in milliseconds)
    timeout: 500

# Packet checks
packet:
  # Timer detection
  timer:
    enabled: true
    violation-threshold: 20
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cTimer§8)"
    description: "Detects game speed manipulation"
    
    # Maximum packets per second
    max-packets: 25
    
    # Minimum packets per second
    min-packets: 15
  
  # BadPackets detection
  badpackets:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cBadPackets§8)"
    description: "Detects invalid or malicious packets"
  
  # Ping Spoof detection
  pingspoof:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cPingSpoof§8)"
    description: "Detects artificial ping manipulation"

  # General packet analysis
  packet:
    enabled: true
    violation-threshold: 20
    punish-command: "kick %player% §cIceAC §8| §fPacket manipulation"
    description: "Analyzes packet patterns and detects anomalies"

    # Maximum packets per second
    max-packets-per-second: 100

    # Maximum movement packets per second
    max-movement-packets-per-second: 22

    # Maximum keep alive delay in milliseconds
    max-keep-alive-delay: 5000

  # Blinker/Disabler detection
  blinker:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fPacket manipulation §8(§cBlinker§8)"
    description: "Detects packet manipulation and blinker cheats"

    # Maximum packet delay in milliseconds
    max-packet-delay: 2000

    # Maximum queued packets
    max-queued-packets: 50

    # Blink threshold in milliseconds
    blink-threshold: 1000

# World interaction checks
world:
  # Nuker detection
  nuker:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cNuker§8)"
    description: "Detects breaking blocks too quickly"
    
    # Maximum blocks per second
    max-blocks-per-second: 10
  
  # FastPlace detection
  fastplace:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cFastPlace§8)"
    description: "Detects placing blocks too quickly"
    
    # Maximum blocks per second
    max-blocks-per-second: 12
  
  # XRay detection
  xray:
    enabled: true
    violation-threshold: 50
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cXRay§8)"
    description: "Detects unusual ore mining patterns"
    
    # Whether to use pattern analysis
    use-pattern-analysis: true
    
    # Whether to use block visibility analysis
    use-visibility-analysis: true

# Exploit checks
exploit:
  # Crash exploit detection
  crash:
    enabled: true
    violation-threshold: 5
    punish-command: "ban %player% §cIceAC §8| §fServer crash attempt"
    description: "Detects attempts to crash the server"
  
  # Inventory exploit detection
  inventory:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fUnfair advantage §8(§cInventory§8)"
    description: "Detects inventory manipulation exploits"
  
  # Command exploit detection
  command:
    enabled: true
    violation-threshold: 10
    punish-command: "kick %player% §cIceAC §8| §fCommand exploit attempt"
    description: "Detects command spam or exploitation"
    
    # Maximum commands per second
    max-commands-per-second: 5

# Other checks
other:
  # Chat spam detection
  chatspam:
    enabled: true
    violation-threshold: 10
    punish-command: "mute %player% 5m §cIceAC §8| §fChat spam"
    description: "Detects chat spam"
    
    # Maximum messages per second
    max-messages-per-second: 3
    
    # Maximum similar messages allowed
    max-similar-messages: 3
  
  # Anti-AFK detection
  antiafk:
    enabled: true
    violation-threshold: 15
    punish-command: "kick %player% §cIceAC §8| §fAFK bypass"
    description: "Detects AFK bypass tools"