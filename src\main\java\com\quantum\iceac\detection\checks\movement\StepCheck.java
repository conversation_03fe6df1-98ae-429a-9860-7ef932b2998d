package com.quantum.iceac.detection.checks.movement;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.checks.Check;
import com.quantum.iceac.detection.checks.CheckType;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.prevention.rules.Rule;
import com.quantum.iceac.prevention.rules.RuleManager;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

/**
 * Check for Step (stepping up blocks higher than allowed)
 */
public class StepCheck extends Check {

    private static final double DEFAULT_MAX_STEP_HEIGHT = 0.6; // Default max step height (without jump)
    private static final double DEFAULT_MAX_JUMP_HEIGHT = 1.25; // Default max jump height (with jump)
    
    public StepCheck(IceAC plugin) {
        super(plugin, "Step", CheckType.MOVEMENT);
        setDescription("Detects stepping up blocks higher than allowed");
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerMove(PlayerMoveEvent event) {
        // Check if event is cancelled
        if (event.isCancelled()) {
            return;
        }
        
        Player player = event.getPlayer();
        Location from = event.getFrom();
        Location to = event.getTo();
        
        // Skip if player hasn't moved vertically
        if (from.getY() == to.getY()) {
            return;
        }
        
        // Get player data
        PlayerData playerData = getPlayerData(player);
        if (playerData == null || playerData.isExempt()) {
            return;
        }
        
        // Skip if player is in creative or spectator mode
        if (player.getGameMode() == GameMode.CREATIVE || player.getGameMode() == GameMode.SPECTATOR) {
            return;
        }
        
        // Skip if player has permission to fly
        if (player.isFlying() || player.getAllowFlight()) {
            return;
        }
        
        // Skip if player is gliding with elytra
        if (player.isGliding()) {
            return;
        }
        
        // Skip if player is in a vehicle
        if (player.isInsideVehicle()) {
            return;
        }
        
        // Skip if player is in water or lava
        Block block = player.getLocation().getBlock();
        if (block.getType() == Material.WATER || block.getType() == Material.LAVA) {
            return;
        }
        
        // Skip if player is on a ladder or vine
        if (block.getType() == Material.LADDER || block.getType() == Material.VINE) {
            return;
        }
        
        // Skip if player is in web
        if (block.getType() == Material.COBWEB) {
            return;
        }
        
        // Calculate step height
        double deltaY = to.getY() - from.getY();
        
        // Skip if player is not stepping up
        if (deltaY <= 0) {
            return;
        }
        
        // Check if player was on ground before stepping
        boolean wasOnGround = isOnGround(from);
        
        // Skip if player wasn't on ground
        if (!wasOnGround) {
            return;
        }
        
        // Get max step height from rules if available
        double maxStepHeight = DEFAULT_MAX_STEP_HEIGHT;
        double maxJumpHeight = DEFAULT_MAX_JUMP_HEIGHT;
        RuleManager ruleManager = getPlugin().getPreventionSystem().getRuleManager();
        
        if (ruleManager != null && ruleManager.isInitialized()) {
            Rule stepHeightRule = ruleManager.getRule("movement.step.height");
            if (stepHeightRule != null) {
                maxStepHeight = stepHeightRule.getValue();
            }
            
            Rule jumpHeightRule = ruleManager.getRule("movement.step.jump_height");
            if (jumpHeightRule != null) {
                maxJumpHeight = jumpHeightRule.getValue();
            }
        }
        
        // Apply jump boost effect if player has it
        if (player.hasPotionEffect(PotionEffectType.JUMP)) {
            int level = 0;
            for (PotionEffect effect : player.getActivePotionEffects()) {
                if (effect.getType().equals(PotionEffectType.JUMP)) {
                    level = effect.getAmplifier() + 1;
                    break;
                }
            }
            maxJumpHeight += level * 0.5;
        }
        
        // Check if player is stepping up too high
        if (deltaY > maxStepHeight && deltaY > maxJumpHeight) {
            // Flag player for violation
            flag(player, "Stepping too high " + String.format("%.2f", deltaY) + " > " + 
                    String.format("%.2f", maxJumpHeight));
            LoggerUtil.debug(player.getName() + " failed Step (Height: " + 
                    String.format("%.2f", deltaY) + ", Max: " + String.format("%.2f", maxJumpHeight) + ")");
        } else if (deltaY > maxStepHeight && deltaY <= maxJumpHeight) {
            // Check if player is jumping
            if (!playerData.isJumping()) {
                // Flag player for violation
                flag(player, "Stepping without jumping " + String.format("%.2f", deltaY) + " > " + 
                        String.format("%.2f", maxStepHeight));
                LoggerUtil.debug(player.getName() + " failed Step (No jump, Height: " + 
                        String.format("%.2f", deltaY) + ", Max: " + String.format("%.2f", maxStepHeight) + ")");
            }
        }
        
        // Reset jumping state
        playerData.setJumping(false);
    }
    
    /**
     * Checks if a location is on the ground
     * @param location The location
     * @return True if location is on ground, false otherwise
     */
    private boolean isOnGround(Location location) {
        Location checkLoc = location.clone();
        checkLoc.setY(checkLoc.getY() - 0.1);
        return checkLoc.getBlock().getType().isSolid();
    }
}