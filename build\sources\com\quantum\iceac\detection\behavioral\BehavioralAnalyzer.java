package com.quantum.iceac.detection.behavioral;

import com.quantum.iceac.IceAC;
import com.quantum.iceac.detection.data.PlayerData;
import com.quantum.iceac.detection.neural.NeuralNetworkEngine;
import com.quantum.iceac.utils.LoggerUtil;
import org.bukkit.entity.Player;

import java.util.*;

/**
 * Behavioral analyzer using machine learning to detect suspicious patterns
 */
public class BehavioralAnalyzer {

    private final IceAC plugin;
    private final Map<UUID, PlayerBehaviorProfile> behaviorProfiles;
    private final Map<UUID, List<BehaviorEvent>> recentEvents;
    private boolean initialized = false;
    
    // Analysis parameters
    private static final int MAX_EVENTS_PER_PLAYER = 1000;
    private static final long EVENT_RETENTION_TIME = 300000; // 5 minutes
    private static final double SUSPICIOUS_THRESHOLD = 0.75;
    
    public BehavioralAnalyzer(IceAC plugin) {
        this.plugin = plugin;
        this.behaviorProfiles = new HashMap<>();
        this.recentEvents = new HashMap<>();
    }
    
    /**
     * Initializes the behavioral analyzer
     */
    public void initialize() {
        if (!plugin.getConfig().getBoolean("detection.behavioral-analysis.enabled", true)) {
            LoggerUtil.info("Behavioral analysis is disabled.");
            return;
        }
        
        LoggerUtil.info("Initializing Behavioral Analyzer...");
        
        // Start background analysis task
        startAnalysisTask();
        
        initialized = true;
        LoggerUtil.info("Behavioral Analyzer initialized successfully.");
    }
    
    /**
     * Records a behavior event for analysis
     * @param player The player
     * @param eventType The type of event
     * @param data Additional event data
     */
    public void recordEvent(Player player, BehaviorEventType eventType, Map<String, Object> data) {
        if (!initialized) {
            return;
        }
        
        UUID uuid = player.getUniqueId();
        
        // Create behavior event
        BehaviorEvent event = new BehaviorEvent(eventType, System.currentTimeMillis(), data);
        
        // Add to recent events
        recentEvents.computeIfAbsent(uuid, k -> new ArrayList<>()).add(event);
        
        // Limit event history
        List<BehaviorEvent> events = recentEvents.get(uuid);
        if (events.size() > MAX_EVENTS_PER_PLAYER) {
            events.remove(0);
        }
        
        // Update behavior profile
        updateBehaviorProfile(player, event);
    }
    
    /**
     * Analyzes a player's behavior and returns suspicion level
     * @param player The player to analyze
     * @return Suspicion level (0.0 to 1.0)
     */
    public double analyzeBehavior(Player player) {
        if (!initialized) {
            return 0.0;
        }
        
        UUID uuid = player.getUniqueId();
        PlayerBehaviorProfile profile = behaviorProfiles.get(uuid);
        
        if (profile == null) {
            return 0.0;
        }
        
        // Analyze different aspects of behavior
        double movementSuspicion = analyzeMovementBehavior(player, profile);
        double combatSuspicion = analyzeCombatBehavior(player, profile);
        double timingSuspicion = analyzeTimingBehavior(player, profile);
        double patternSuspicion = analyzePatternBehavior(player, profile);
        
        // Combine suspicion scores
        double overallSuspicion = (movementSuspicion + combatSuspicion + timingSuspicion + patternSuspicion) / 4.0;
        
        // Use neural network for final analysis if available
        NeuralNetworkEngine neuralEngine = plugin.getDetectionEngine().getNeuralNetworkEngine();
        if (neuralEngine != null && neuralEngine.isInitialized()) {
            double neuralSuspicion = neuralEngine.analyzePlayer(player, "behavioral");
            overallSuspicion = (overallSuspicion + neuralSuspicion) / 2.0;
        }
        
        return Math.max(0.0, Math.min(1.0, overallSuspicion));
    }
    
    /**
     * Analyzes movement behavior patterns
     * @param player The player
     * @param profile The behavior profile
     * @return Suspicion level
     */
    private double analyzeMovementBehavior(Player player, PlayerBehaviorProfile profile) {
        double suspicion = 0.0;
        
        // Check movement consistency
        if (profile.getMovementConsistency() > 0.95) {
            suspicion += 0.3; // Very consistent movement is suspicious
        }
        
        // Check direction changes
        if (profile.getAverageDirectionChanges() < 0.1) {
            suspicion += 0.2; // Too few direction changes
        }
        
        // Check speed variations
        if (profile.getSpeedVariation() < 0.05) {
            suspicion += 0.25; // Too consistent speed
        }
        
        return suspicion;
    }
    
    /**
     * Analyzes combat behavior patterns
     * @param player The player
     * @param profile The behavior profile
     * @return Suspicion level
     */
    private double analyzeCombatBehavior(Player player, PlayerBehaviorProfile profile) {
        double suspicion = 0.0;
        
        // Check aim consistency
        if (profile.getAimConsistency() > 0.9) {
            suspicion += 0.4; // Very consistent aim
        }
        
        // Check attack timing
        if (profile.getAttackTimingVariation() < 0.02) {
            suspicion += 0.3; // Too consistent attack timing
        }
        
        // Check hit accuracy
        if (profile.getHitAccuracy() > 0.95) {
            suspicion += 0.3; // Unrealistically high accuracy
        }
        
        return suspicion;
    }
    
    /**
     * Analyzes timing behavior patterns
     * @param player The player
     * @param profile The behavior profile
     * @return Suspicion level
     */
    private double analyzeTimingBehavior(Player player, PlayerBehaviorProfile profile) {
        double suspicion = 0.0;
        
        // Check reaction times
        if (profile.getAverageReactionTime() < 50) {
            suspicion += 0.4; // Unrealistically fast reactions
        }
        
        // Check input timing consistency
        if (profile.getInputTimingConsistency() > 0.95) {
            suspicion += 0.3; // Too consistent input timing
        }
        
        return suspicion;
    }
    
    /**
     * Analyzes pattern behavior
     * @param player The player
     * @param profile The behavior profile
     * @return Suspicion level
     */
    private double analyzePatternBehavior(Player player, PlayerBehaviorProfile profile) {
        double suspicion = 0.0;
        
        // Check for repetitive patterns
        if (profile.getPatternRepetition() > 0.8) {
            suspicion += 0.4; // High pattern repetition
        }
        
        // Check for inhuman precision
        if (profile.getPrecisionScore() > 0.95) {
            suspicion += 0.3; // Inhuman precision
        }
        
        return suspicion;
    }
    
    /**
     * Updates a player's behavior profile
     * @param player The player
     * @param event The behavior event
     */
    private void updateBehaviorProfile(Player player, BehaviorEvent event) {
        UUID uuid = player.getUniqueId();
        PlayerBehaviorProfile profile = behaviorProfiles.computeIfAbsent(uuid, 
                k -> new PlayerBehaviorProfile(player));
        
        profile.addEvent(event);
        
        // Analyze recent events for patterns
        List<BehaviorEvent> events = recentEvents.get(uuid);
        if (events != null && events.size() > 10) {
            analyzeEventPatterns(profile, events);
        }
    }
    
    /**
     * Analyzes patterns in behavior events
     * @param profile The behavior profile
     * @param events The list of events
     */
    private void analyzeEventPatterns(PlayerBehaviorProfile profile, List<BehaviorEvent> events) {
        // Analyze movement patterns
        analyzeMovementPatterns(profile, events);
        
        // Analyze combat patterns
        analyzeCombatPatterns(profile, events);
        
        // Analyze timing patterns
        analyzeTimingPatterns(profile, events);
    }
    
    /**
     * Analyzes movement patterns in events
     * @param profile The behavior profile
     * @param events The list of events
     */
    private void analyzeMovementPatterns(PlayerBehaviorProfile profile, List<BehaviorEvent> events) {
        List<BehaviorEvent> movementEvents = events.stream()
                .filter(e -> e.getType() == BehaviorEventType.MOVEMENT)
                .toList();
        
        if (movementEvents.size() < 5) return;
        
        // Calculate movement consistency
        double consistency = calculateMovementConsistency(movementEvents);
        profile.setMovementConsistency(consistency);
        
        // Calculate direction changes
        double directionChanges = calculateDirectionChanges(movementEvents);
        profile.setAverageDirectionChanges(directionChanges);
        
        // Calculate speed variation
        double speedVariation = calculateSpeedVariation(movementEvents);
        profile.setSpeedVariation(speedVariation);
    }
    
    /**
     * Analyzes combat patterns in events
     * @param profile The behavior profile
     * @param events The list of events
     */
    private void analyzeCombatPatterns(PlayerBehaviorProfile profile, List<BehaviorEvent> events) {
        List<BehaviorEvent> combatEvents = events.stream()
                .filter(e -> e.getType() == BehaviorEventType.COMBAT)
                .toList();
        
        if (combatEvents.size() < 3) return;
        
        // Calculate aim consistency
        double aimConsistency = calculateAimConsistency(combatEvents);
        profile.setAimConsistency(aimConsistency);
        
        // Calculate attack timing variation
        double timingVariation = calculateAttackTimingVariation(combatEvents);
        profile.setAttackTimingVariation(timingVariation);
        
        // Calculate hit accuracy
        double hitAccuracy = calculateHitAccuracy(combatEvents);
        profile.setHitAccuracy(hitAccuracy);
    }
    
    /**
     * Analyzes timing patterns in events
     * @param profile The behavior profile
     * @param events The list of events
     */
    private void analyzeTimingPatterns(PlayerBehaviorProfile profile, List<BehaviorEvent> events) {
        // Calculate average reaction time
        double reactionTime = calculateAverageReactionTime(events);
        profile.setAverageReactionTime(reactionTime);
        
        // Calculate input timing consistency
        double inputConsistency = calculateInputTimingConsistency(events);
        profile.setInputTimingConsistency(inputConsistency);
    }
    
    // Helper methods for calculations (simplified implementations)
    
    private double calculateMovementConsistency(List<BehaviorEvent> events) {
        // Simplified calculation
        return Math.random() * 0.3 + 0.7; // Placeholder
    }
    
    private double calculateDirectionChanges(List<BehaviorEvent> events) {
        return Math.random() * 0.5; // Placeholder
    }
    
    private double calculateSpeedVariation(List<BehaviorEvent> events) {
        return Math.random() * 0.2; // Placeholder
    }
    
    private double calculateAimConsistency(List<BehaviorEvent> events) {
        return Math.random() * 0.4 + 0.6; // Placeholder
    }
    
    private double calculateAttackTimingVariation(List<BehaviorEvent> events) {
        return Math.random() * 0.1; // Placeholder
    }
    
    private double calculateHitAccuracy(List<BehaviorEvent> events) {
        return Math.random() * 0.3 + 0.7; // Placeholder
    }
    
    private double calculateAverageReactionTime(List<BehaviorEvent> events) {
        return Math.random() * 100 + 100; // Placeholder (100-200ms)
    }
    
    private double calculateInputTimingConsistency(List<BehaviorEvent> events) {
        return Math.random() * 0.4 + 0.6; // Placeholder
    }
    
    /**
     * Starts the background analysis task
     */
    private void startAnalysisTask() {
        plugin.getServer().getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            // Clean up old events
            cleanupOldEvents();
            
            // Analyze all online players
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                double suspicion = analyzeBehavior(player);
                
                if (suspicion > SUSPICIOUS_THRESHOLD) {
                    LoggerUtil.debug(player.getName() + " has suspicious behavior score: " + 
                            String.format("%.3f", suspicion));
                }
            }
        }, 20L * 30, 20L * 30); // Run every 30 seconds
    }
    
    /**
     * Cleans up old events
     */
    private void cleanupOldEvents() {
        long now = System.currentTimeMillis();
        
        for (List<BehaviorEvent> events : recentEvents.values()) {
            events.removeIf(event -> now - event.getTimestamp() > EVENT_RETENTION_TIME);
        }
    }
    
    /**
     * Gets a player's behavior profile
     * @param player The player
     * @return The behavior profile
     */
    public PlayerBehaviorProfile getBehaviorProfile(Player player) {
        return behaviorProfiles.get(player.getUniqueId());
    }
    
    /**
     * Checks if the analyzer is initialized
     * @return True if initialized
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Shuts down the behavioral analyzer
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        behaviorProfiles.clear();
        recentEvents.clear();
        initialized = false;
        LoggerUtil.info("Behavioral Analyzer shut down.");
    }
}
