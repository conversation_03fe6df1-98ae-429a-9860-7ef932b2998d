# IceAC Punishments Configuration
# This file contains settings for how violations are punished

# General punishment settings
general:
  # Whether to enable automatic punishments
  enabled: true
  
  # Whether to broadcast punishments to all players
  broadcast: true
  
  # Whether to log punishments to a file
  log: true
  
  # Whether to reset violations on disconnect
  reset-on-disconnect: false
  
  # Time in minutes after which violations are decreased
  violation-decay-time: 10
  
  # Amount of violations decreased after decay time
  violation-decay-amount: 1

# Punishment thresholds and actions
thresholds:
  # Low severity violations
  low:
    # Violations required to trigger
    violations: 15
    
    # Actions to take
    actions:
      # Whether to notify staff
      notify: true
      
      # Whether to kick the player
      kick: true
      
      # Whether to ban the player
      ban: false
      
      # Ban duration in minutes (0 = permanent)
      ban-duration: 0
      
      # Commands to execute
      commands:
        - "kick %player% §cIceAC §8| §fUnfair advantage §8(§c%check%§8)"
  
  # Medium severity violations
  medium:
    # Violations required to trigger
    violations: 25
    
    # Actions to take
    actions:
      # Whether to notify staff
      notify: true
      
      # Whether to kick the player
      kick: true
      
      # Whether to ban the player
      ban: true
      
      # Ban duration in minutes (0 = permanent)
      ban-duration: 1440
      
      # Commands to execute
      commands:
        - "tempban %player% 1d §cIceAC §8| §fUnfair advantage §8(§c%check%§8)"
  
  # High severity violations
  high:
    # Violations required to trigger
    violations: 40
    
    # Actions to take
    actions:
      # Whether to notify staff
      notify: true
      
      # Whether to kick the player
      kick: true
      
      # Whether to ban the player
      ban: true
      
      # Ban duration in minutes (0 = permanent)
      ban-duration: 0
      
      # Commands to execute
      commands:
        - "ban %player% §cIceAC §8| §fUnfair advantage §8(§c%check%§8)"

# Check-specific punishment settings
# These override the general thresholds
checks:
  # Combat checks
  killaura:
    severity: high
    violations: 20
    actions:
      notify: true
      kick: true
      ban: true
      ban-duration: 4320
      commands:
        - "tempban %player% 3d §cIceAC §8| §fUnfair advantage §8(§cKillAura§8)"
  
  reach:
    severity: medium
    violations: 25
    actions:
      notify: true
      kick: true
      ban: true
      ban-duration: 1440
      commands:
        - "tempban %player% 1d §cIceAC §8| §fUnfair advantage §8(§cReach§8)"
  
  # Movement checks
  speed:
    severity: medium
    violations: 30
    actions:
      notify: true
      kick: true
      ban: false
      ban-duration: 0
      commands:
        - "kick %player% §cIceAC §8| §fUnfair advantage §8(§cSpeed§8)"
  
  fly:
    severity: high
    violations: 20
    actions:
      notify: true
      kick: true
      ban: true
      ban-duration: 1440
      commands:
        - "tempban %player% 1d §cIceAC §8| §fUnfair advantage §8(§cFly§8)"
  
  # Exploit checks
  crash:
    severity: high
    violations: 5
    actions:
      notify: true
      kick: true
      ban: true
      ban-duration: 0
      commands:
        - "ban %player% §cIceAC §8| §fServer crash attempt"
  
  # Other checks
  chatspam:
    severity: low
    violations: 10
    actions:
      notify: true
      kick: false
      ban: false
      ban-duration: 0
      commands:
        - "mute %player% 5m §cIceAC §8| §fChat spam"

# Advanced punishment settings
advanced:
  # Whether to use machine learning to adjust punishment thresholds
  use-machine-learning: true
  
  # Whether to consider player history when punishing
  consider-history: true
  
  # Maximum violations before permanent ban
  max-violations: 100
  
  # Whether to escalate punishments for repeat offenders
  escalate-punishments: true
  
  # Factor by which to escalate punishment duration
  escalation-factor: 2.0
  
  # Maximum number of punishments before permanent ban
  max-punishments: 5
  
  # Whether to notify server admins via Discord
  notify-discord: false
  
  # Discord webhook URL
  discord-webhook: ""