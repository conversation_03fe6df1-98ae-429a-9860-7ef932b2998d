@echo off
echo Creating FIXED IceAC JAR...

REM Create libs directory
if not exist "build\libs" mkdir "build\libs"

REM Copy resources to classes directory
xcopy "src\main\resources\*" "build\classes\java\main\" /E /Y

REM Create JAR file with FIXED suffix
cd build\classes\java\main
jar -cf "..\..\..\libs\IceAC-1.0.0-FIXED.jar" *
cd ..\..\..\..

echo.
echo FIXED JAR created successfully!
echo Location: build\libs\IceAC-1.0.0-FIXED.jar
echo.

REM Show JAR contents
echo JAR Contents:
jar -tf "build\libs\IceAC-1.0.0-FIXED.jar" | findstr /C:"CommandManager" /C:"LoggerUtil" /C:"IceAC.class"

echo.
echo Build completed successfully!
echo.
echo CHANGES IN THIS VERSION:
echo - Fixed CommandManager NullPointerException
echo - Added minimal help system
echo - Disabled bStats metrics for minimal build
echo - All core systems working
pause
