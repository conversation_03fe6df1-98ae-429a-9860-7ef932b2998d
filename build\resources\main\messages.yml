# IceAC Messages Configuration
# This file contains all the messages used by the anti-cheat system
# You can use color codes with the & symbol

prefix: "&cIceAC &8| &f"

# General messages
general:
  no-permission: "%prefix%You don't have permission to use this command."
  player-only: "%prefix%This command can only be used by players."
  reload-start: "%prefix%Reloading IceAC..."
  reload-complete: "%prefix%IceAC has been reloaded successfully."
  plugin-enabled: "%prefix%IceAC has been enabled. Protecting your server with advanced anti-cheat technology."
  plugin-disabled: "%prefix%IceAC has been disabled."
  debug-enabled: "%prefix%Debug mode has been enabled."
  debug-disabled: "%prefix%Debug mode has been disabled."

# Command messages
commands:
  help-header: "&8&m-----&r &cIceAC Commands &8&m-----"
  help-format: "&c/%command% &8- &f%description%"
  help-footer: "&8&m--------------------------"
  
  status-header: "&8&m-----&r &cIceAC Status &8&m-----"
  status-version: "&8• &fVersion: &c%version%"
  status-uptime: "&8• &fUptime: &c%uptime%"
  status-checks: "&8• &fChecks: &c%enabled%&8/&c%total%"
  status-violations: "&8• &fViolations: &c%violations%"
  status-punishments: "&8• &fPunishments: &c%punishments%"
  status-footer: "&8&m--------------------------"
  
  alerts-enabled: "%prefix%Alerts have been &aenabled&f."
  alerts-disabled: "%prefix%Alerts have been &cdisabled&f."
  
  whitelist-add: "%prefix%&a%player% &fhas been added to the whitelist."
  whitelist-remove: "%prefix%&c%player% &fhas been removed from the whitelist."
  whitelist-list-header: "&8&m-----&r &cWhitelisted Players &8&m-----"
  whitelist-list-format: "&8• &f%player% &8(&7%reason%&8)"
  whitelist-list-empty: "&8• &fNo players are whitelisted."
  whitelist-list-footer: "&8&m--------------------------"
  
  check-enable: "%prefix%Check &a%check% &fhas been enabled."
  check-disable: "%prefix%Check &c%check% &fhas been disabled."
  check-info-header: "&8&m-----&r &c%check% Information &8&m-----"
  check-info-type: "&8• &fType: &c%type%"
  check-info-description: "&8• &fDescription: &c%description%"
  check-info-status: "&8• &fStatus: %status%"
  check-info-violations: "&8• &fViolations: &c%violations%"
  check-info-punishments: "&8• &fPunishments: &c%punishments%"
  check-info-footer: "&8&m--------------------------"
  
  check-list-header: "&8&m-----&r &cIceAC Checks &8&m-----"
  check-list-format: "&8• &f%check% &8[%status%&8] - &7%description%"
  check-list-footer: "&8&m--------------------------"

# Alert messages
alerts:
  format: "%prefix%&c%player% &ffailed &c%check% &8(&7%info%&8) &8[&c%violations%&8]"
  hover: "&8• &f&c%player%\n&8• &fCheck: &c%check%\n&8• &f&7%info%\n&8• &f&c%violations%\n&8• &f&c%ping%ms\n&8• &f&c%tps%"
  click-command: "/tp %player%"
  click-hint: "&7Click to teleport to &c%player%"

# Punishment messages
punishments:
  kick-message: "&cIceAC &8| &fYou have been kicked for &c%reason%"
  ban-message: "&cIceAC &8| &fYou have been banned for &c%reason%"
  ban-broadcast: "%prefix%&c%player% &fhas been banned for &c%reason%"
  kick-broadcast: "%prefix%&c%player% &fhas been kicked for &c%reason%"

# Dashboard messages
dashboard:
  header: "&8&m-----&r &cIceAC Dashboard &8&m-----"
  uptime: "&8• &fUptime: &c%uptime%"
  violations: "&8• &fViolations: &c%violations% &8(&c%violations_per_minute%&8/min)"
  punishments: "&8• &fPunishments: &c%punishments%"
  check-stats-header: "&8• &fCheck Statistics:"
  check-stats-format: "  &8- &c%type%&8: &f%violations% &8(&c%percentage%%&8)"
  top-checks-header: "&8• &fTop Checks:"
  top-checks-format: "  &8- &c%check%&8: &f%violations% &8(&c%percentage%%&8)"
  footer: "&8&m--------------------------"
  enabled: "%prefix%Dashboard has been &aenabled&f."
  disabled: "%prefix%Dashboard has been &cdisabled&f."

# Error messages
errors:
  check-not-found: "%prefix%Check &c%check% &fnot found."
  player-not-found: "%prefix%Player &c%player% &fnot found."
  invalid-argument: "%prefix%Invalid argument: &c%argument%"
  invalid-number: "%prefix%Invalid number: &c%number%"
  command-usage: "%prefix%Usage: &c%usage%"
  dependency-missing: "%prefix%Required dependency &c%dependency% &fis missing."
  config-error: "%prefix%Error in configuration: &c%error%"
  internal-error: "%prefix%An internal error occurred. Please check the console."